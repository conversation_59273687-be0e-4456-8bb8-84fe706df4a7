-- IMT Insurance Claims System Database Initialization

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE claims_db'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'claims_db')\gexec

-- Connect to the database
\c claims_db;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Set timezone
SET timezone = 'UTC';

-- Create indexes for better performance (will be created by Django migrations)
-- These are just placeholders for any custom indexes you might need

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE claims_db TO claims_user;
