# Git
.git
.gitignore
README.md

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
.venv/

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Development files
.env
.env.local
.env.development
.env.test
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/

# Documentation
docs/
*.md

# Deployment
docker-compose*.yml
Dockerfile*
k8s/
nginx/

# Temporary files
*.tmp
*.temp
test_*.py
test_*.sh
cookies.txt
test_cookies.txt
