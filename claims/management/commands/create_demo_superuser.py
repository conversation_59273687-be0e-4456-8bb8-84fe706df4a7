"""
Django management command to create a demo superuser for the IMT Insurance Claims System.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import IntegrityError

User = get_user_model()


class Command(BaseCommand):
    help = 'Create a demo superuser for IMT Insurance Claims System'

    def handle(self, *args, **options):
        try:
            # Check if superuser already exists
            if User.objects.filter(username='imtadmin').exists():
                self.stdout.write(
                    self.style.WARNING('⚠️  Superuser "imtadmin" already exists')
                )
                return

            # Create superuser
            superuser = User.objects.create_superuser(
                username='imtadmin',
                email='<EMAIL>',
                password='imtadmin123',
                first_name='IMT',
                last_name='Administrator',
                user_type='admin',
                phone_number='(*************',
                address='123 IMT Insurance Plaza, Chicago, IL 60601'
            )

            self.stdout.write(
                self.style.SUCCESS('✅ Demo superuser created successfully!')
            )
            self.stdout.write('')
            self.stdout.write('🔑 Login Credentials:')
            self.stdout.write(f'   Username: {superuser.username}')
            self.stdout.write('   Password: imtadmin123')
            self.stdout.write('')
            self.stdout.write('🌐 Access Points:')
            self.stdout.write('   Admin Panel: http://localhost:8000/admin/')
            self.stdout.write('   Main System: http://localhost:8000/')
            self.stdout.write('')

        except IntegrityError as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error creating superuser: {e}')
            )
