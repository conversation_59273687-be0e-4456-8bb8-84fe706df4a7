"""
Django management command to seed the IMT Insurance Claims System database
with comprehensive sample data for demonstration purposes.
"""

import random
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from django.core.files.base import ContentFile
import io

from users.models import User, UserProfile
from claims.models import Claim, ClaimNote, Task
from documents.models import Document, DocumentVersion
from payments.models import Payment, Settlement

User = get_user_model()


class Command(BaseCommand):
    help = 'Seed the database with comprehensive sample data for IMT Insurance Claims System'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before seeding',
        )
        parser.add_argument(
            '--users',
            type=int,
            default=25,
            help='Number of users to create (default: 25)',
        )
        parser.add_argument(
            '--claims',
            type=int,
            default=50,
            help='Number of claims to create (default: 50)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🏢 Starting IMT Insurance Claims System Database Seeding...')
        )

        if options['clear']:
            self.clear_data()

        with transaction.atomic():
            # Create users first
            users = self.create_users(options['users'])
            self.stdout.write(
                self.style.SUCCESS(f'✅ Created {len(users)} users')
            )

            # Create claims
            claims = self.create_claims(users, options['claims'])
            self.stdout.write(
                self.style.SUCCESS(f'✅ Created {len(claims)} claims')
            )

            # Create documents for claims
            documents = self.create_documents(claims, users)
            self.stdout.write(
                self.style.SUCCESS(f'✅ Created {len(documents)} documents')
            )

            # Create payments and settlements
            payments = self.create_payments(claims, users)
            self.stdout.write(
                self.style.SUCCESS(f'✅ Created {len(payments)} payments')
            )

            # Create claim notes and tasks
            notes = self.create_claim_notes(claims, users)
            tasks = self.create_tasks(claims, users)
            self.stdout.write(
                self.style.SUCCESS(f'✅ Created {len(notes)} notes and {len(tasks)} tasks')
            )

        self.stdout.write(
            self.style.SUCCESS('🎉 Database seeding completed successfully!')
        )
        self.print_summary()

    def clear_data(self):
        """Clear existing data"""
        self.stdout.write('🗑️  Clearing existing data...')

        # Clear in reverse dependency order
        Payment.objects.all().delete()
        Settlement.objects.all().delete()
        Document.objects.all().delete()
        DocumentVersion.objects.all().delete()
        Task.objects.all().delete()
        ClaimNote.objects.all().delete()
        Claim.objects.all().delete()
        UserProfile.objects.all().delete()
        User.objects.filter(is_superuser=False).delete()

        self.stdout.write(self.style.WARNING('✅ Existing data cleared'))

    def create_users(self, count):
        """Create sample users with different roles"""
        users = []

        # Sample data for realistic users
        first_names = [
            'James', 'Mary', 'John', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda',
            'William', 'Elizabeth', 'David', 'Barbara', 'Richard', 'Susan', 'Joseph', 'Jessica',
            'Thomas', 'Sarah', 'Christopher', 'Karen', 'Charles', 'Nancy', 'Daniel', 'Lisa',
            'Matthew', 'Betty', 'Anthony', 'Helen', 'Mark', 'Sandra', 'Donald', 'Donna'
        ]

        last_names = [
            'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
            'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson',
            'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson',
            'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker'
        ]

        # Create administrators (2-3)
        admin_count = min(3, max(1, count // 15))
        for i in range(admin_count):
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            username = f"admin_{first_name.lower()}_{last_name.lower()}"

            user = User.objects.create_user(
                username=username,
                email=f"{username}@imtins.com",
                password="admin123",
                first_name=first_name,
                last_name=last_name,
                user_type='admin',
                phone_number=self.generate_phone(),
                address=self.generate_address(),
                is_staff=True
            )
            users.append(user)

        # Create claims adjusters (15-20% of users)
        adjuster_count = max(2, count // 5)
        for i in range(adjuster_count):
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            username = f"adjuster_{first_name.lower()}_{last_name.lower()}"

            user = User.objects.create_user(
                username=username,
                email=f"{username}@imtins.com",
                password="adjuster123",
                first_name=first_name,
                last_name=last_name,
                user_type='adjuster',
                phone_number=self.generate_phone(),
                address=self.generate_address(),
                is_staff=True
            )
            users.append(user)

        # Create insurance agents (10% of users)
        agent_count = max(1, count // 10)
        for i in range(agent_count):
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            username = f"agent_{first_name.lower()}_{last_name.lower()}"

            user = User.objects.create_user(
                username=username,
                email=f"{username}@imtins.com",
                password="agent123",
                first_name=first_name,
                last_name=last_name,
                user_type='agent',
                phone_number=self.generate_phone(),
                address=self.generate_address(),
            )
            users.append(user)

        # Create customers (remaining users)
        remaining_count = count - len(users)
        for i in range(remaining_count):
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            username = f"{first_name.lower()}.{last_name.lower()}{random.randint(1, 999)}"

            user = User.objects.create_user(
                username=username,
                email=f"{username}@email.com",
                password="customer123",
                first_name=first_name,
                last_name=last_name,
                user_type='customer',
                phone_number=self.generate_phone(),
                address=self.generate_address(),
                date_of_birth=self.generate_birth_date(),
                policy_number=self.generate_policy_number()
            )
            users.append(user)

        # Create user profiles
        for user in users:
            UserProfile.objects.create(
                user=user,
                bio=f"IMT Insurance {user.get_user_type_display()}",
                emergency_contact_name=f"{random.choice(first_names)} {random.choice(last_names)}",
                emergency_contact_phone=self.generate_phone(),
                preferred_communication=random.choice(['email', 'phone', 'sms'])
            )

        return users

    def create_claims(self, users, count):
        """Create sample insurance claims"""
        claims = []
        customers = [u for u in users if u.user_type == 'customer']
        adjusters = [u for u in users if u.user_type == 'adjuster']

        # Sample incident scenarios
        auto_incidents = [
            "Rear-end collision at traffic light on Main Street",
            "Side-impact collision at intersection of Oak and Pine",
            "Vehicle struck by falling tree during storm",
            "Parking lot fender bender at shopping center",
            "Hit and run incident on Highway 101",
            "Collision with deer on rural road",
            "Vehicle damaged by hail during severe weather",
            "Multi-car accident on interstate during rush hour"
        ]

        home_incidents = [
            "Kitchen fire caused by faulty electrical wiring",
            "Water damage from burst pipe in basement",
            "Roof damage from severe windstorm",
            "Burglary with stolen electronics and jewelry",
            "Basement flooding during heavy rainfall",
            "Tree fell on house during storm",
            "Smoke damage from neighbor's house fire",
            "Vandalism to exterior property"
        ]

        business_incidents = [
            "Office fire damaged equipment and inventory",
            "Break-in with theft of computer equipment",
            "Water damage from sprinkler system malfunction",
            "Customer slip and fall in retail store",
            "Equipment failure causing business interruption",
            "Cyber attack compromising customer data",
            "Vehicle accident involving company delivery truck",
            "Property damage from severe weather"
        ]

        for i in range(count):
            claimant = random.choice(customers)
            claim_type = random.choice(['auto', 'home', 'business', 'umbrella'])

            # Select appropriate incident based on claim type
            if claim_type == 'auto':
                incident_desc = random.choice(auto_incidents)
                location = random.choice([
                    "Main Street and 1st Avenue", "Highway 101 near Exit 15",
                    "Walmart Parking Lot", "Downtown Business District",
                    "Residential area on Oak Street", "Interstate 95 Mile Marker 42"
                ])
            elif claim_type == 'home':
                incident_desc = random.choice(home_incidents)
                location = claimant.address or "123 Main Street, Anytown, ST 12345"
            elif claim_type == 'business':
                incident_desc = random.choice(business_incidents)
                location = random.choice([
                    "123 Business Park Drive", "456 Industrial Boulevard",
                    "789 Commerce Center", "321 Main Street Office Building"
                ])
            else:  # umbrella
                incident_desc = "Liability claim requiring umbrella coverage"
                location = "Various locations"

            # Generate realistic dates
            incident_date = timezone.now() - timedelta(
                days=random.randint(1, 365),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            )

            # Determine status based on how old the claim is
            days_old = (timezone.now() - incident_date).days
            if days_old < 7:
                status = random.choice(['submitted', 'under_review'])
            elif days_old < 30:
                status = random.choice(['under_review', 'investigating', 'pending_documents'])
            elif days_old < 90:
                status = random.choice(['investigating', 'pending_documents', 'approved', 'denied'])
            else:
                status = random.choice(['approved', 'denied', 'closed'])

            # Generate amounts based on claim type
            if claim_type == 'auto':
                estimated_amount = Decimal(random.randint(500, 25000))
                deductible = Decimal(random.choice([250, 500, 1000]))
            elif claim_type == 'home':
                estimated_amount = Decimal(random.randint(1000, 75000))
                deductible = Decimal(random.choice([500, 1000, 2500]))
            elif claim_type == 'business':
                estimated_amount = Decimal(random.randint(2000, 100000))
                deductible = Decimal(random.choice([1000, 2500, 5000]))
            else:  # umbrella
                estimated_amount = Decimal(random.randint(10000, 500000))
                deductible = Decimal(random.choice([0, 1000, 5000]))

            # Set approved amount for approved claims
            approved_amount = None
            if status in ['approved', 'closed']:
                # Approved amount is usually 70-95% of estimated
                approved_amount = estimated_amount * Decimal(random.uniform(0.7, 0.95))
                approved_amount = approved_amount.quantize(Decimal('0.01'))

            claim = Claim.objects.create(
                claimant=claimant,
                assigned_adjuster=random.choice(adjusters) if adjusters else None,
                claim_type=claim_type,
                status=status,
                priority=random.choice(['low', 'medium', 'high', 'urgent']),
                incident_date=incident_date,
                incident_location=location,
                incident_description=incident_desc,
                estimated_damage_amount=estimated_amount,
                approved_amount=approved_amount,
                deductible_amount=deductible,
                closed_at=timezone.now() if status == 'closed' else None
            )
            claims.append(claim)

        return claims

    def create_documents(self, claims, users):
        """Create sample documents for claims"""
        documents = []

        document_templates = {
            'photo': [
                'Accident Scene Photo 1', 'Accident Scene Photo 2', 'Vehicle Damage Front',
                'Vehicle Damage Rear', 'Property Damage Overview', 'Close-up Damage Detail'
            ],
            'police_report': [
                'Official Police Report', 'Traffic Accident Report', 'Incident Report'
            ],
            'medical_report': [
                'Emergency Room Report', 'Doctor Visit Summary', 'Physical Therapy Report',
                'Medical Bills and Records'
            ],
            'estimate': [
                'Auto Repair Estimate', 'Property Repair Estimate', 'Contractor Quote',
                'Insurance Adjuster Estimate'
            ],
            'receipt': [
                'Towing Receipt', 'Rental Car Receipt', 'Hotel Receipt', 'Medical Bills',
                'Repair Shop Invoice', 'Replacement Item Receipt'
            ],
            'form': [
                'Claim Form', 'Proof of Loss Form', 'Medical Authorization Form',
                'Property Inventory Form'
            ]
        }

        for claim in claims:
            # Each claim gets 2-6 documents
            num_docs = random.randint(2, 6)

            for _ in range(num_docs):
                doc_type = random.choice(list(document_templates.keys()))
                title = random.choice(document_templates[doc_type])

                # Add claim-specific context to title
                if claim.claim_type == 'auto':
                    title = f"{title} - {claim.claim_number}"
                elif claim.claim_type == 'home':
                    title = f"{title} - Property Claim {claim.claim_number}"
                elif claim.claim_type == 'business':
                    title = f"{title} - Business Claim {claim.claim_number}"

                # Create a dummy file content
                file_content = f"Sample {doc_type} document for claim {claim.claim_number}\n"
                file_content += f"Generated on {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                file_content += f"Claim Type: {claim.get_claim_type_display()}\n"
                file_content += f"Incident Date: {claim.incident_date.strftime('%Y-%m-%d')}\n"
                file_content += "This is a sample document for demonstration purposes."

                # Create file extension based on document type
                if doc_type == 'photo':
                    filename = f"{title.replace(' ', '_').lower()}.jpg"
                elif doc_type in ['police_report', 'medical_report', 'form']:
                    filename = f"{title.replace(' ', '_').lower()}.pdf"
                else:
                    filename = f"{title.replace(' ', '_').lower()}.pdf"

                document = Document.objects.create(
                    claim=claim,
                    uploaded_by=random.choice([claim.claimant, claim.assigned_adjuster]) if claim.assigned_adjuster else claim.claimant,
                    title=title,
                    description=f"Document related to {claim.get_claim_type_display()} claim {claim.claim_number}",
                    document_type=doc_type,
                    file=ContentFile(file_content.encode(), filename),
                    is_required=random.choice([True, False]),
                    is_approved=random.choice([True, False]) if claim.status in ['approved', 'closed'] else False
                )
                documents.append(document)

        return documents

    def create_payments(self, claims, users):
        """Create payments and settlements for approved claims"""
        payments = []
        approved_claims = [c for c in claims if c.status in ['approved', 'closed'] and c.approved_amount]

        for claim in approved_claims:
            # Create settlement first
            settlement = Settlement.objects.create(
                claim=claim,
                total_amount=claim.approved_amount,
                deductible_applied=claim.deductible_amount,
                net_amount=claim.approved_amount - claim.deductible_amount,
                settlement_date=timezone.now().date() - timedelta(days=random.randint(1, 30)),
                settlement_notes=f"Settlement approved for claim {claim.claim_number}",
                approved_by=claim.assigned_adjuster
            )

            # Create payment(s) for the settlement
            payment_method = random.choice(['check', 'direct_deposit', 'wire_transfer'])

            payment = Payment.objects.create(
                claim=claim,
                payee=claim.claimant,
                processed_by=claim.assigned_adjuster,
                amount=settlement.net_amount,
                payment_method=payment_method,
                status=random.choice(['completed', 'processing', 'pending']),
                reference_number=f"PAY-{claim.claim_number}-{random.randint(1000, 9999)}",
                notes=f"Payment for approved claim {claim.claim_number}",
                scheduled_date=timezone.now().date() + timedelta(days=random.randint(1, 7)),
                processed_date=timezone.now() - timedelta(days=random.randint(1, 14))
            )

            # Add payment method specific details
            if payment_method == 'direct_deposit':
                payment.bank_name = random.choice(['Chase Bank', 'Bank of America', 'Wells Fargo', 'Citibank'])
                payment.account_number = f"****{random.randint(1000, 9999)}"
                payment.routing_number = f"{random.randint(*********, *********)}"
            elif payment_method == 'check':
                payment.mailing_address = claim.claimant.address

            payment.save()
            payments.append(payment)

        return payments

    def create_claim_notes(self, claims, users):
        """Create notes and comments for claims"""
        notes = []
        adjusters = [u for u in users if u.user_type in ['adjuster', 'admin']]

        note_templates = [
            "Initial claim review completed. All required documents received.",
            "Contacted claimant to schedule property inspection.",
            "Adjuster inspection completed. Damage assessment in progress.",
            "Additional documentation requested from claimant.",
            "Medical records review completed by medical examiner.",
            "Repair estimates received and under review.",
            "Claim approved for settlement processing.",
            "Settlement amount calculated based on policy coverage.",
            "Payment processing initiated.",
            "Claim closed. All documentation filed.",
            "Follow-up required with claimant regarding missing documents.",
            "Coordination with repair facility completed.",
            "Subrogation investigation initiated.",
            "Legal review completed - no issues identified."
        ]

        for claim in claims:
            # Each claim gets 1-5 notes
            num_notes = random.randint(1, 5)

            for i in range(num_notes):
                author = random.choice(adjusters) if adjusters else claim.assigned_adjuster
                if not author:
                    continue

                note_text = random.choice(note_templates)
                is_internal = random.choice([True, False])

                # Add claim-specific context
                note_text = f"{note_text} (Claim: {claim.claim_number})"

                note = ClaimNote.objects.create(
                    claim=claim,
                    author=author,
                    note=note_text,
                    is_internal=is_internal,
                    created_at=claim.created_at + timedelta(days=i, hours=random.randint(1, 23))
                )
                notes.append(note)

        return notes

    def create_tasks(self, claims, users):
        """Create tasks for claim processing"""
        tasks = []
        adjusters = [u for u in users if u.user_type in ['adjuster', 'admin']]

        task_templates = [
            ("Review claim documentation", "Review all submitted documents for completeness and accuracy"),
            ("Schedule property inspection", "Contact claimant to schedule on-site property inspection"),
            ("Obtain repair estimates", "Collect repair estimates from approved contractors"),
            ("Medical records review", "Review medical records and bills for injury claims"),
            ("Process settlement payment", "Initiate payment processing for approved settlement"),
            ("Update claim status", "Update claim status in system and notify claimant"),
            ("File claim documentation", "Organize and file all claim-related documents"),
            ("Contact claimant", "Follow up with claimant regarding claim status"),
            ("Coordinate with vendors", "Coordinate with repair facilities and service providers"),
            ("Legal review", "Submit claim for legal review if required")
        ]

        for claim in claims:
            # Each claim gets 1-4 tasks
            num_tasks = random.randint(1, 4)

            for i in range(num_tasks):
                if not adjusters:
                    continue

                title, description = random.choice(task_templates)
                assigned_to = random.choice(adjusters)
                created_by = claim.assigned_adjuster or assigned_to

                # Determine task status based on claim status
                if claim.status in ['closed', 'approved']:
                    task_status = 'completed'
                elif claim.status in ['under_review', 'investigating']:
                    task_status = random.choice(['pending', 'in_progress', 'completed'])
                else:
                    task_status = random.choice(['pending', 'in_progress'])

                due_date = claim.created_at + timedelta(days=random.randint(7, 30))
                completed_at = None
                if task_status == 'completed':
                    completed_at = due_date - timedelta(days=random.randint(1, 5))

                task = Task.objects.create(
                    claim=claim,
                    assigned_to=assigned_to,
                    created_by=created_by,
                    title=title,
                    description=description,
                    status=task_status,
                    priority=random.choice(['low', 'medium', 'high']),
                    due_date=due_date,
                    completed_at=completed_at
                )
                tasks.append(task)

        return tasks

    # Helper methods for generating realistic data
    def generate_phone(self):
        """Generate a realistic phone number"""
        area_codes = ['555', '312', '415', '212', '713', '404', '602', '503']
        area_code = random.choice(area_codes)
        number = f"{random.randint(100, 999)}-{random.randint(1000, 9999)}"
        return f"({area_code}) {number}"

    def generate_address(self):
        """Generate a realistic address"""
        street_numbers = [random.randint(100, 9999) for _ in range(10)]
        street_names = [
            'Main Street', 'Oak Avenue', 'Pine Road', 'Elm Drive', 'Maple Lane',
            'Cedar Boulevard', 'Park Avenue', 'First Street', 'Second Avenue',
            'Washington Street', 'Lincoln Drive', 'Jefferson Road'
        ]
        cities = [
            'Springfield', 'Franklin', 'Georgetown', 'Madison', 'Riverside',
            'Fairview', 'Midtown', 'Hillside', 'Lakewood', 'Oakwood'
        ]
        states = ['IL', 'CA', 'TX', 'NY', 'FL', 'OH', 'PA', 'MI', 'GA', 'NC']

        street_num = random.choice(street_numbers)
        street_name = random.choice(street_names)
        city = random.choice(cities)
        state = random.choice(states)
        zip_code = random.randint(10000, 99999)

        return f"{street_num} {street_name}, {city}, {state} {zip_code}"

    def generate_birth_date(self):
        """Generate a realistic birth date for adults"""
        start_date = datetime(1940, 1, 1)
        end_date = datetime(2000, 12, 31)
        time_between = end_date - start_date
        days_between = time_between.days
        random_days = random.randrange(days_between)
        return start_date + timedelta(days=random_days)

    def generate_policy_number(self):
        """Generate a realistic IMT policy number"""
        prefix = "IMT"
        year = random.randint(2020, 2024)
        number = random.randint(100000, 999999)
        return f"{prefix}-{year}-{number}"

    def print_summary(self):
        """Print a summary of created data"""
        self.stdout.write("\n" + "="*60)
        self.stdout.write(self.style.SUCCESS("📊 DATABASE SEEDING SUMMARY"))
        self.stdout.write("="*60)

        # Count users by type
        user_counts = {}
        for user_type, _ in User.USER_TYPES:
            count = User.objects.filter(user_type=user_type).count()
            user_counts[user_type] = count

        self.stdout.write(f"👥 Users Created:")
        for user_type, count in user_counts.items():
            display_name = dict(User.USER_TYPES)[user_type]
            self.stdout.write(f"   - {display_name}: {count}")

        # Count claims by status
        self.stdout.write(f"\n📋 Claims Created:")
        for status, display in Claim.STATUS_CHOICES:
            count = Claim.objects.filter(status=status).count()
            if count > 0:
                self.stdout.write(f"   - {display}: {count}")

        # Count claims by type
        self.stdout.write(f"\n🏠 Claims by Type:")
        for claim_type, display in Claim.CLAIM_TYPES:
            count = Claim.objects.filter(claim_type=claim_type).count()
            if count > 0:
                self.stdout.write(f"   - {display}: {count}")

        # Other statistics
        doc_count = Document.objects.count()
        payment_count = Payment.objects.count()
        settlement_count = Settlement.objects.count()
        note_count = ClaimNote.objects.count()
        task_count = Task.objects.count()

        self.stdout.write(f"\n📄 Documents: {doc_count}")
        self.stdout.write(f"💰 Payments: {payment_count}")
        self.stdout.write(f"🤝 Settlements: {settlement_count}")
        self.stdout.write(f"📝 Notes: {note_count}")
        self.stdout.write(f"✅ Tasks: {task_count}")

        self.stdout.write("\n" + "="*60)
        self.stdout.write(self.style.SUCCESS("🎉 IMT Insurance Claims System Ready for Demo!"))
        self.stdout.write("="*60)

        # Login information
        self.stdout.write(f"\n🔑 Sample Login Credentials:")
        self.stdout.write(f"   Admin: admin_[name]_[surname] / admin123")
        self.stdout.write(f"   Adjuster: adjuster_[name]_[surname] / adjuster123")
        self.stdout.write(f"   Customer: [firstname].[lastname][number] / customer123")
        self.stdout.write(f"   Agent: agent_[name]_[surname] / agent123")
        self.stdout.write(f"\n🌐 Access the system at: http://localhost:8000")
        self.stdout.write(f"🔧 Admin panel: http://localhost:8000/admin/")
        self.stdout.write("")
