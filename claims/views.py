from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Count
from django.http import JsonResponse
from django.utils import timezone
from .models import <PERSON><PERSON><PERSON>, ClaimNote, Task
from .forms import ClaimForm, ClaimNoteForm, TaskForm
from users.models import User


@login_required
def dashboard(request):
    """Main dashboard view"""
    context = {}

    if request.user.user_type == 'customer':
        # Customer dashboard
        user_claims = Claim.objects.filter(claimant=request.user)
        context.update({
            'total_claims': user_claims.count(),
            'pending_claims': user_claims.filter(status__in=['submitted', 'under_review']).count(),
            'approved_claims': user_claims.filter(status='approved').count(),
            'recent_claims': user_claims[:5],
        })

    elif request.user.user_type in ['adjuster', 'admin']:
        # Adjuster/Admin dashboard
        all_claims = Claim.objects.all()
        assigned_claims = Claim.objects.filter(assigned_adjuster=request.user)
        pending_tasks = Task.objects.filter(assigned_to=request.user, status='pending')

        context.update({
            'total_claims': all_claims.count(),
            'assigned_claims': assigned_claims.count(),
            'pending_tasks': pending_tasks.count(),
            'recent_claims': all_claims[:5],
            'urgent_claims': all_claims.filter(priority='urgent')[:5],
            'pending_tasks_list': pending_tasks[:5],
        })

    return render(request, 'claims/dashboard.html', context)


@login_required
def submit_claim(request):
    """Submit a new claim"""
    if request.user.user_type != 'customer':
        messages.error(request, 'Only customers can submit claims.')
        return redirect('dashboard')

    if request.method == 'POST':
        form = ClaimForm(request.POST)
        if form.is_valid():
            claim = form.save(commit=False)
            claim.claimant = request.user
            claim.save()

            # Auto-assign to an available adjuster
            adjuster = User.objects.filter(user_type='adjuster').first()
            if adjuster:
                claim.assigned_adjuster = adjuster
                claim.save()

            messages.success(request, f'Claim {claim.claim_number} submitted successfully!')
            return redirect('claims:detail', claim_id=claim.id)
    else:
        form = ClaimForm()

    return render(request, 'claims/submit.html', {'form': form})


@login_required
def claim_list(request):
    """List all claims (for adjusters/admins)"""
    if request.user.user_type not in ['adjuster', 'admin']:
        messages.error(request, 'Access denied.')
        return redirect('dashboard')

    claims = Claim.objects.all()

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        claims = claims.filter(status=status_filter)

    # Filter by priority
    priority_filter = request.GET.get('priority')
    if priority_filter:
        claims = claims.filter(priority=priority_filter)

    # Search
    search = request.GET.get('search')
    if search:
        claims = claims.filter(
            Q(claim_number__icontains=search) |
            Q(claimant__first_name__icontains=search) |
            Q(claimant__last_name__icontains=search) |
            Q(incident_location__icontains=search)
        )

    claims = claims.order_by('-created_at')

    context = {
        'claims': claims,
        'status_choices': Claim.STATUS_CHOICES,
        'priority_choices': Claim.PRIORITY_CHOICES,
        'current_status': status_filter,
        'current_priority': priority_filter,
        'search_query': search,
    }

    return render(request, 'claims/list.html', context)


@login_required
def my_claims(request):
    """List user's own claims"""
    if request.user.user_type != 'customer':
        return redirect('claims:list')

    claims = Claim.objects.filter(claimant=request.user).order_by('-created_at')

    return render(request, 'claims/my_claims.html', {'claims': claims})


@login_required
def claim_detail(request, claim_id):
    """View claim details"""
    claim = get_object_or_404(Claim, id=claim_id)

    # Check permissions
    if request.user.user_type == 'customer' and claim.claimant != request.user:
        messages.error(request, 'You can only view your own claims.')
        return redirect('dashboard')

    notes = claim.notes.all()
    if request.user.user_type == 'customer':
        # Customers can't see internal notes
        notes = notes.filter(is_internal=False)

    tasks = claim.tasks.all()
    documents = claim.documents.all()

    context = {
        'claim': claim,
        'notes': notes,
        'tasks': tasks,
        'documents': documents,
        'can_edit': request.user.user_type in ['adjuster', 'admin'] or
                   (request.user.user_type == 'customer' and claim.status == 'submitted'),
    }

    return render(request, 'claims/detail.html', context)


@login_required
def edit_claim(request, claim_id):
    """Edit claim (for adjusters/admins)"""
    claim = get_object_or_404(Claim, id=claim_id)

    if request.user.user_type not in ['adjuster', 'admin']:
        messages.error(request, 'Access denied.')
        return redirect('claims:detail', claim_id=claim.id)

    if request.method == 'POST':
        from .forms import ClaimEditForm
        form = ClaimEditForm(request.POST, instance=claim)
        if form.is_valid():
            form.save()
            messages.success(request, 'Claim updated successfully!')
            return redirect('claims:detail', claim_id=claim.id)
    else:
        from .forms import ClaimEditForm
        form = ClaimEditForm(instance=claim)

    return render(request, 'claims/edit.html', {'form': form, 'claim': claim})


@login_required
def add_note(request, claim_id):
    """Add a note to a claim"""
    claim = get_object_or_404(Claim, id=claim_id)

    # Check permissions
    if request.user.user_type == 'customer' and claim.claimant != request.user:
        messages.error(request, 'Access denied.')
        return redirect('dashboard')

    if request.method == 'POST':
        form = ClaimNoteForm(request.POST)
        if form.is_valid():
            note = form.save(commit=False)
            note.claim = claim
            note.author = request.user

            # Customers can't create internal notes
            if request.user.user_type == 'customer':
                note.is_internal = False

            note.save()
            messages.success(request, 'Note added successfully!')
            return redirect('claims:detail', claim_id=claim.id)

    return redirect('claims:detail', claim_id=claim.id)


@login_required
def task_list(request):
    """List tasks for the current user"""
    if request.user.user_type not in ['adjuster', 'admin']:
        messages.error(request, 'Access denied.')
        return redirect('dashboard')

    tasks = Task.objects.filter(assigned_to=request.user).order_by('due_date', '-created_at')

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        tasks = tasks.filter(status=status_filter)

    context = {
        'tasks': tasks,
        'status_choices': Task.TASK_STATUS,
        'current_status': status_filter,
    }

    return render(request, 'claims/tasks.html', context)


@login_required
def task_detail(request, task_id):
    """View task details"""
    task = get_object_or_404(Task, id=task_id)

    if request.user.user_type not in ['adjuster', 'admin'] and task.assigned_to != request.user:
        messages.error(request, 'Access denied.')
        return redirect('dashboard')

    return render(request, 'claims/task_detail.html', {'task': task})


@login_required
def complete_task(request, task_id):
    """Mark a task as completed"""
    task = get_object_or_404(Task, id=task_id)

    if task.assigned_to != request.user and request.user.user_type != 'admin':
        messages.error(request, 'You can only complete your own tasks.')
        return redirect('claims:tasks')

    if request.method == 'POST':
        task.status = 'completed'
        task.completed_at = timezone.now()
        task.save()
        messages.success(request, 'Task marked as completed!')

    return redirect('claims:task_detail', task_id=task.id)


@login_required
def add_task(request, claim_id):
    """Add a task to a claim"""
    claim = get_object_or_404(Claim, id=claim_id)

    if request.user.user_type not in ['adjuster', 'admin']:
        messages.error(request, 'Access denied.')
        return redirect('claims:detail', claim_id=claim.id)

    if request.method == 'POST':
        form = TaskForm(request.POST)
        if form.is_valid():
            task = form.save(commit=False)
            task.claim = claim
            task.created_by = request.user
            task.save()
            messages.success(request, 'Task created successfully!')
            return redirect('claims:detail', claim_id=claim.id)
    else:
        form = TaskForm()

    return render(request, 'claims/add_task.html', {'form': form, 'claim': claim})
