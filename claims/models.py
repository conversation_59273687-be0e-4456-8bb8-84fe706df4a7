from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class Claim(models.Model):
    """Main claim model for insurance claims"""

    CLAIM_TYPES = (
        ('auto', 'Auto Insurance'),
        ('home', 'Home Insurance'),
        ('business', 'Business Insurance'),
        ('umbrella', 'Umbrella Insurance'),
    )

    STATUS_CHOICES = (
        ('submitted', 'Submitted'),
        ('under_review', 'Under Review'),
        ('investigating', 'Investigating'),
        ('pending_documents', 'Pending Documents'),
        ('approved', 'Approved'),
        ('denied', 'Denied'),
        ('closed', 'Closed'),
    )

    PRIORITY_CHOICES = (
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    )

    # Basic Information
    claim_number = models.CharField(max_length=20, unique=True, editable=False)
    claimant = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='claims')
    assigned_adjuster = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_claims',
        limit_choices_to={'user_type': 'adjuster'}
    )

    # Claim Details
    claim_type = models.CharField(max_length=20, choices=CLAIM_TYPES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='submitted')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')

    # Incident Information
    incident_date = models.DateTimeField()
    incident_location = models.TextField()
    incident_description = models.TextField()

    # Financial Information
    estimated_damage_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    approved_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    deductible_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    closed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.claim_number:
            self.claim_number = self.generate_claim_number()
        super().save(*args, **kwargs)

    def generate_claim_number(self):
        """Generate a unique claim number"""
        year = timezone.now().year
        random_part = str(uuid.uuid4().hex)[:8].upper()
        return f"IMT-{year}-{random_part}"

    def __str__(self):
        return f"Claim {self.claim_number} - {self.claimant.full_name}"


class ClaimNote(models.Model):
    """Notes and comments on claims"""
    claim = models.ForeignKey(Claim, on_delete=models.CASCADE, related_name='notes')
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    note = models.TextField()
    is_internal = models.BooleanField(default=False)  # Internal notes not visible to customers
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Note on {self.claim.claim_number} by {self.author.username}"


class Task(models.Model):
    """Tasks related to claim processing"""

    TASK_STATUS = (
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    )

    claim = models.ForeignKey(Claim, on_delete=models.CASCADE, related_name='tasks')
    assigned_to = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='assigned_tasks')
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_tasks')

    title = models.CharField(max_length=200)
    description = models.TextField()
    status = models.CharField(max_length=20, choices=TASK_STATUS, default='pending')
    priority = models.CharField(max_length=10, choices=Claim.PRIORITY_CHOICES, default='medium')

    due_date = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['due_date', '-created_at']

    def __str__(self):
        return f"Task: {self.title} ({self.claim.claim_number})"
