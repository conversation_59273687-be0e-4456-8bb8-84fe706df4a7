from django import forms
from django.utils import timezone
from .models import <PERSON><PERSON><PERSON>, ClaimNote, Task


class ClaimForm(forms.ModelForm):
    """Form for submitting new claims"""
    
    class Meta:
        model = Claim
        fields = [
            'claim_type', 'incident_date', 'incident_location', 
            'incident_description', 'estimated_damage_amount'
        ]
        widgets = {
            'incident_date': forms.DateTimeInput(
                attrs={'type': 'datetime-local', 'class': 'form-control'}
            ),
            'incident_location': forms.Textarea(
                attrs={'rows': 3, 'class': 'form-control'}
            ),
            'incident_description': forms.Textarea(
                attrs={'rows': 5, 'class': 'form-control'}
            ),
            'estimated_damage_amount': forms.NumberInput(
                attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}
            ),
            'claim_type': forms.Select(attrs={'class': 'form-select'}),
        }
        labels = {
            'claim_type': 'Type of Insurance Claim',
            'incident_date': 'Date and Time of Incident',
            'incident_location': 'Location of Incident',
            'incident_description': 'Description of Incident',
            'estimated_damage_amount': 'Estimated Damage Amount ($)',
        }
    
    def clean_incident_date(self):
        incident_date = self.cleaned_data['incident_date']
        if incident_date > timezone.now():
            raise forms.ValidationError("Incident date cannot be in the future.")
        return incident_date


class ClaimEditForm(forms.ModelForm):
    """Form for editing claims (admin/adjuster use)"""
    
    class Meta:
        model = Claim
        fields = [
            'status', 'priority', 'assigned_adjuster', 'approved_amount', 
            'deductible_amount', 'incident_description'
        ]
        widgets = {
            'status': forms.Select(attrs={'class': 'form-select'}),
            'priority': forms.Select(attrs={'class': 'form-select'}),
            'assigned_adjuster': forms.Select(attrs={'class': 'form-select'}),
            'approved_amount': forms.NumberInput(
                attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}
            ),
            'deductible_amount': forms.NumberInput(
                attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}
            ),
            'incident_description': forms.Textarea(
                attrs={'rows': 5, 'class': 'form-control'}
            ),
        }


class ClaimNoteForm(forms.ModelForm):
    """Form for adding notes to claims"""
    
    class Meta:
        model = ClaimNote
        fields = ['note', 'is_internal']
        widgets = {
            'note': forms.Textarea(
                attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Add your note here...'}
            ),
            'is_internal': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'note': 'Note',
            'is_internal': 'Internal Note (not visible to customer)',
        }


class TaskForm(forms.ModelForm):
    """Form for creating tasks"""
    
    class Meta:
        model = Task
        fields = ['title', 'description', 'assigned_to', 'priority', 'due_date']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(
                attrs={'rows': 4, 'class': 'form-control'}
            ),
            'assigned_to': forms.Select(attrs={'class': 'form-select'}),
            'priority': forms.Select(attrs={'class': 'form-select'}),
            'due_date': forms.DateTimeInput(
                attrs={'type': 'datetime-local', 'class': 'form-control'}
            ),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Limit assigned_to to adjusters and admins
        from users.models import User
        self.fields['assigned_to'].queryset = User.objects.filter(
            user_type__in=['adjuster', 'admin']
        )


class ClaimSearchForm(forms.Form):
    """Form for searching claims"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Search by claim number, customer name, or location...'
            }
        )
    )
    status = forms.ChoiceField(
        choices=[('', 'All Statuses')] + list(Claim.STATUS_CHOICES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    priority = forms.ChoiceField(
        choices=[('', 'All Priorities')] + list(Claim.PRIORITY_CHOICES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    claim_type = forms.ChoiceField(
        choices=[('', 'All Types')] + list(Claim.CLAIM_TYPES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
