from django.contrib import admin
from .models import <PERSON>laim, ClaimNote, Task


class ClaimNoteInline(admin.TabularInline):
    model = ClaimNote
    extra = 0
    readonly_fields = ('created_at',)


class TaskInline(admin.TabularInline):
    model = Task
    extra = 0
    readonly_fields = ('created_at',)


@admin.register(Claim)
class ClaimAdmin(admin.ModelAdmin):
    list_display = ('claim_number', 'claimant', 'claim_type', 'status', 'priority', 'created_at')
    list_filter = ('claim_type', 'status', 'priority', 'created_at')
    search_fields = ('claim_number', 'claimant__username', 'claimant__email', 'incident_location')
    readonly_fields = ('claim_number', 'created_at', 'updated_at')
    inlines = [ClaimNoteInline, TaskInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('claim_number', 'claimant', 'assigned_adjuster', 'claim_type', 'status', 'priority')
        }),
        ('Incident Details', {
            'fields': ('incident_date', 'incident_location', 'incident_description')
        }),
        ('Financial Information', {
            'fields': ('estimated_damage_amount', 'approved_amount', 'deductible_amount')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'closed_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ClaimNote)
class ClaimNoteAdmin(admin.ModelAdmin):
    list_display = ('claim', 'author', 'is_internal', 'created_at')
    list_filter = ('is_internal', 'created_at')
    search_fields = ('claim__claim_number', 'author__username', 'note')
    readonly_fields = ('created_at',)


@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    list_display = ('title', 'claim', 'assigned_to', 'status', 'priority', 'due_date', 'created_at')
    list_filter = ('status', 'priority', 'created_at', 'due_date')
    search_fields = ('title', 'claim__claim_number', 'assigned_to__username')
    readonly_fields = ('created_at', 'updated_at')
