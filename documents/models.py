from django.db import models
from django.conf import settings
import os


class Document(models.Model):
    """Document model for claim-related files"""

    DOCUMENT_TYPES = (
        ('photo', 'Photo/Image'),
        ('police_report', 'Police Report'),
        ('medical_report', 'Medical Report'),
        ('estimate', 'Repair Estimate'),
        ('receipt', 'Receipt'),
        ('form', 'Insurance Form'),
        ('other', 'Other'),
    )

    claim = models.ForeignKey('claims.Claim', on_delete=models.CASCADE, related_name='documents')
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES, default='other')
    file = models.FileField(upload_to='claim_documents/')
    file_size = models.PositiveIntegerField(null=True, blank=True)

    is_required = models.BooleanField(default=False)
    is_approved = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if self.file:
            self.file_size = self.file.size
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.title} - {self.claim.claim_number}"

    @property
    def file_extension(self):
        return os.path.splitext(self.file.name)[1].lower()

    @property
    def is_image(self):
        return self.file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']


class DocumentVersion(models.Model):
    """Version control for documents"""
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='versions')
    version_number = models.PositiveIntegerField(default=1)
    file = models.FileField(upload_to='document_versions/')
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    change_notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-version_number']
        unique_together = ['document', 'version_number']

    def __str__(self):
        return f"{self.document.title} v{self.version_number}"
