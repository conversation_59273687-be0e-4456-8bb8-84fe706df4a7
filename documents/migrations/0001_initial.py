# Generated by Django 4.2.21 on 2025-05-28 19:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('document_type', models.Char<PERSON>ield(choices=[('photo', 'Photo/Image'), ('police_report', 'Police Report'), ('medical_report', 'Medical Report'), ('estimate', 'Repair Estimate'), ('receipt', 'Receipt'), ('form', 'Insurance Form'), ('other', 'Other')], default='other', max_length=20)),
                ('file', models.FileField(upload_to='claim_documents/')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True)),
                ('is_required', models.BooleanField(default=False)),
                ('is_approved', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DocumentVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version_number', models.PositiveIntegerField(default=1)),
                ('file', models.FileField(upload_to='document_versions/')),
                ('change_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='versions', to='documents.document')),
            ],
            options={
                'ordering': ['-version_number'],
            },
        ),
    ]
