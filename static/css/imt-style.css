/* IMT Insurance Claims System Styling */

:root {
    --imt-red: #e53e3e;
    --imt-dark-red: #c53030;
    --imt-light-red: #fc8181;
    --imt-white: #ffffff;
    --imt-gray: #f7fafc;
    --imt-dark-gray: #2d3748;
    --imt-light-gray: #edf2f7;
    --imt-border: #e2e8f0;
    --imt-text: #2d3748;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: var(--imt-gray);
    color: var(--imt-text);
    line-height: 1.6;
}

/* Header Styling - IMT Red Theme */
.navbar-brand {
    font-weight: bold;
    color: var(--imt-white) !important;
    font-size: 1.5rem;
}

.navbar {
    background: linear-gradient(135deg, var(--imt-red) 0%, var(--imt-dark-red) 100%) !important;
    border: none;
    box-shadow: 0 2px 10px rgba(229, 62, 62, 0.3);
    padding: 1rem 0;
}

.navbar-nav .nav-link {
    color: var(--imt-white) !important;
    font-weight: 500;
    padding: 0.75rem 1.5rem !important;
    border-radius: 25px;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Button Styling - IMT Red Theme */
.btn-primary {
    background: linear-gradient(135deg, var(--imt-red) 0%, var(--imt-dark-red) 100%);
    border: none;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(229, 62, 62, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--imt-dark-red) 0%, #a02626 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
    border: none;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(56, 161, 105, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #2f855a 0%, #276749 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(56, 161, 105, 0.4);
}

.btn-outline-primary {
    color: var(--imt-red);
    border: 2px solid var(--imt-red);
    background: transparent;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-outline-primary:hover {
    background: var(--imt-red);
    border-color: var(--imt-red);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4);
}

/* Card Styling - IMT Theme */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    background: var(--imt-white);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--imt-red) 0%, var(--imt-dark-red) 100%);
    border: none;
    font-weight: 600;
    color: var(--imt-white);
    padding: 1.25rem 1.5rem;
}

/* Dashboard Styling - IMT Theme */
.dashboard-card {
    transition: all 0.3s ease;
    border-radius: 15px;
    background: var(--imt-white);
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.dashboard-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.stat-card {
    text-align: center;
    padding: 2.5rem 1.5rem;
    background: linear-gradient(135deg, var(--imt-white) 0%, var(--imt-light-gray) 100%);
    border-radius: 15px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--imt-red);
    text-shadow: 0 2px 4px rgba(229, 62, 62, 0.2);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--imt-dark-gray);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

/* Form Styling - IMT Theme */
.form-control {
    border: 2px solid var(--imt-border);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--imt-red);
    box-shadow: 0 0 0 0.2rem rgba(229, 62, 62, 0.25);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: var(--imt-text);
    margin-bottom: 0.75rem;
}

/* Table Styling - IMT Theme */
.table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, var(--imt-red) 0%, var(--imt-dark-red) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1.25rem 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: var(--imt-light-gray);
    transform: scale(1.01);
}

/* Status Badges */
.status-submitted { background-color: #17a2b8; }
.status-under-review { background-color: #ffc107; color: #000; }
.status-investigating { background-color: #fd7e14; }
.status-pending-documents { background-color: #dc3545; }
.status-approved { background-color: #28a745; }
.status-denied { background-color: #6c757d; }
.status-closed { background-color: #343a40; }

/* Priority Badges */
.priority-low { background-color: #28a745; }
.priority-medium { background-color: #ffc107; color: #000; }
.priority-high { background-color: #fd7e14; }
.priority-urgent { background-color: #dc3545; }

/* Footer - IMT Theme */
.footer {
    background: linear-gradient(135deg, var(--imt-dark-gray) 0%, #1a202c 100%);
    color: white;
    padding: 3rem 0 2rem 0;
    margin-top: 4rem;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--imt-red) 0%, var(--imt-dark-red) 100%);
}

.footer a {
    color: #cbd5e0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--imt-light-red);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }

    .navbar-brand {
        font-size: 1.25rem;
    }
}

/* Loading spinner - IMT Theme */
.spinner-border-imt {
    color: var(--imt-red);
}

/* Alert styling - IMT Theme */
.alert-info {
    background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
    border: 2px solid var(--imt-red);
    border-radius: 15px;
    color: var(--imt-text);
    padding: 1.25rem;
}

/* Sidebar for admin - IMT Theme */
.sidebar {
    background: linear-gradient(180deg, var(--imt-white) 0%, var(--imt-light-gray) 100%);
    min-height: calc(100vh - 76px);
    border-right: 3px solid var(--imt-red);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: var(--imt-text);
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--imt-red) 0%, var(--imt-dark-red) 100%);
    color: white;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(229, 62, 62, 0.3);
}

/* File upload styling - IMT Theme */
.file-upload-area {
    border: 3px dashed var(--imt-red);
    border-radius: 15px;
    padding: 3rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, var(--imt-white) 0%, var(--imt-light-gray) 100%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.file-upload-area::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(229, 62, 62, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.3s ease;
    opacity: 0;
}

.file-upload-area:hover {
    border-color: var(--imt-dark-red);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(229, 62, 62, 0.2);
}

.file-upload-area:hover::before {
    opacity: 1;
    animation: shimmer 2s infinite;
}

.file-upload-area.dragover {
    border-color: var(--imt-dark-red);
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    transform: scale(1.02);
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* IMT Logo and Branding */
.navbar-brand::before {
    content: "◆";
    color: var(--imt-white);
    font-size: 1.5rem;
    margin-right: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Hero Section Styling */
.hero-section {
    background: linear-gradient(135deg, var(--imt-red) 0%, var(--imt-dark-red) 100%);
    color: var(--imt-white);
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    opacity: 0.3;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.25rem;
    font-weight: 400;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Page Headers */
.page-header {
    background: linear-gradient(135deg, var(--imt-white) 0%, var(--imt-light-gray) 100%);
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-bottom: 4px solid var(--imt-red);
}

.page-title {
    color: var(--imt-text);
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
}

/* Enhanced Card Animations */
@keyframes cardPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.card.pulse {
    animation: cardPulse 2s infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--imt-light-gray);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--imt-red) 0%, var(--imt-dark-red) 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--imt-dark-red) 0%, #a02626 100%);
}
