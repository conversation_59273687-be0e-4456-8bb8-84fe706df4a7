"""
Health check views for IMT Insurance Claims System.
"""

from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
from django.conf import settings
import time


def health_check(request):
    """
    Health check endpoint for load balancers and monitoring.
    """
    health_status = {
        'status': 'healthy',
        'timestamp': int(time.time()),
        'version': '1.0.0',
        'service': 'IMT Insurance Claims System',
        'checks': {}
    }
    
    # Database health check
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            health_status['checks']['database'] = 'healthy'
    except Exception as e:
        health_status['status'] = 'unhealthy'
        health_status['checks']['database'] = f'unhealthy: {str(e)}'
    
    # Cache health check (if Red<PERSON> is configured)
    try:
        cache.set('health_check', 'ok', 30)
        if cache.get('health_check') == 'ok':
            health_status['checks']['cache'] = 'healthy'
        else:
            health_status['checks']['cache'] = 'unhealthy: cache test failed'
    except Exception as e:
        health_status['checks']['cache'] = f'unhealthy: {str(e)}'
    
    # Return appropriate HTTP status code
    status_code = 200 if health_status['status'] == 'healthy' else 503
    
    return JsonResponse(health_status, status=status_code)


def readiness_check(request):
    """
    Readiness check endpoint for Kubernetes.
    """
    return JsonResponse({
        'status': 'ready',
        'timestamp': int(time.time()),
        'service': 'IMT Insurance Claims System'
    })


def liveness_check(request):
    """
    Liveness check endpoint for Kubernetes.
    """
    return JsonResponse({
        'status': 'alive',
        'timestamp': int(time.time()),
        'service': 'IMT Insurance Claims System'
    })
