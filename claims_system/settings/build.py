"""
Build-time settings for IMT Insurance Claims System.
Used during Docker build process when environment variables are not available.
"""

from .base import *

# Build-time settings - minimal configuration for collectstatic
DEBUG = False

ALLOWED_HOSTS = ['*']  # Permissive for build time

# Dummy database configuration for build time
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

# Static files configuration
STATIC_ROOT = '/app/staticfiles'
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Disable cache for build
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Minimal logging for build
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
    },
}

# Disable email during build
EMAIL_BACKEND = 'django.core.mail.backends.dummy.EmailBackend'

# Security settings (minimal for build)
SECRET_KEY = 'build-time-secret-key-not-for-production'
SECURE_SSL_REDIRECT = False
