Metadata-Version: 2.1
Name: django-widget-tweaks
Version: 1.5.0
Summary: Tweak the form field rendering in templates, not in python-level form definitions.
Home-page: https://github.com/jazzband/django-widget-tweaks
Author: <PERSON>-email: <EMAIL>
License: MIT license
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Django
Classifier: Framework :: Django :: 3.2
Classifier: Framework :: Django :: 4.1
Classifier: Framework :: Django :: 4.2
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires: django (>=2.2)
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE

====================
django-widget-tweaks
====================

.. image:: https://jazzband.co/static/img/badge.svg
   :target: https://jazzband.co/
   :alt: Jazzband

.. image:: https://img.shields.io/pypi/v/django-widget-tweaks.svg
   :target: https://pypi.python.org/pypi/django-widget-tweaks
   :alt: PyPI Version

.. image:: https://github.com/jazzband/django-widget-tweaks/workflows/Test/badge.svg
   :target: https://github.com/jazzband/django-widget-tweaks/actions
   :alt: GitHub Actions

.. image:: https://codecov.io/gh/jazzband/django-widget-tweaks/branch/master/graph/badge.svg
   :target: https://codecov.io/gh/jazzband/django-widget-tweaks
   :alt: Coverage

Tweak the form field rendering in templates, not in python-level
form definitions. Altering CSS classes and HTML attributes is supported.

That should be enough for designers to customize field presentation (using
CSS and unobtrusive javascript) without touching python code.

License is MIT.

Installation
============

You can get Django Widget Tweaks by using pip::

    $ pip install django-widget-tweaks

To enable `widget_tweaks` in your project you need to add it to `INSTALLED_APPS` in your projects
`settings.py` file:

.. code-block:: python

    INSTALLED_APPS += [
        'widget_tweaks',
    ]

Usage
=====

This app provides two sets of tools that may be used together or standalone:

1. a ``render_field`` template tag for customizing form fields by using an
   HTML-like syntax.
2. several template filters for customizing form field HTML attributes and CSS
   classes

The ``render_field`` tag should be easier to use and should make form field
customizations much easier for designers and front-end developers.

The template filters are more powerful than the ``render_field`` tag, but they
use a more complex and less HTML-like syntax.

render_field
------------

This is a template tag that can be used as an alternative to aforementioned
filters.  This template tag renders a field using a syntax similar to plain
HTML attributes.

Example:

.. code-block:: html+django

    {% load widget_tweaks %}

    <!-- change input type (e.g. to HTML5) -->
    {% render_field form.search_query type="search" %}

    <!-- add/change several attributes -->
    {% render_field form.text rows="20" cols="20" title="Hello, world!" %}

    <!-- append to an attribute -->
    {% render_field form.title class+="css_class_1 css_class_2" %}

    <!-- template variables can be used as attribute values -->
    {% render_field form.text placeholder=form.text.label %}

    <!-- double colon -->
    {% render_field form.search_query v-bind::class="{active:isActive}" %}


For fields rendered with ``{% render_field %}`` tag it is possible
to set error class and required fields class by using
``WIDGET_ERROR_CLASS`` and  ``WIDGET_REQUIRED_CLASS`` template variables:

.. code-block:: html+django

    {% with WIDGET_ERROR_CLASS='my_error' WIDGET_REQUIRED_CLASS='my_required' %}
        {% render_field form.field1 %}
        {% render_field form.field2 %}
        {% render_field form.field3 %}
    {% endwith %}

You can be creative with these variables: e.g. a context processor could
set a default CSS error class on all fields rendered by
``{% render_field %}``.


attr
----
Adds or replaces any single html attribute for the form field.

Examples:

.. code-block:: html+django

    {% load widget_tweaks %}

    <!-- change input type (e.g. to HTML5) -->
    {{ form.search_query|attr:"type:search" }}

    <!-- add/change several attributes -->
    {{ form.text|attr:"rows:20"|attr:"cols:20"|attr:"title:Hello, world!" }}

    <!-- attributes without parameters -->
    {{ form.search_query|attr:"autofocus" }}


    <!-- attributes with double colon Vuejs output: v-bind:class="{active:ValueEnabled}" -->
    {{ form.search_query|attr:"v-bind::class:{active:ValueEnabled}" }}


add_class
---------

Adds CSS class to field element. Split classes by whitespace in order to add
several classes at once.

Example:

.. code-block:: html+django

    {% load widget_tweaks %}

    <!-- add 2 extra css classes to field element -->
    {{ form.title|add_class:"css_class_1 css_class_2" }}

set_data
--------

Sets HTML5 data attribute ( http://ejohn.org/blog/html-5-data-attributes/ ).
Useful for unobtrusive javascript. It is just a shortcut for 'attr' filter
that prepends attribute names with 'data-' string.

Example:

.. code-block:: html+django

    {% load widget_tweaks %}

    <!-- data-filters:"OverText" will be added to input field -->
    {{ form.title|set_data:"filters:OverText" }}

append_attr
-----------

Appends attribute value with extra data.

Example:

.. code-block:: html+django

    {% load widget_tweaks %}

    <!-- add 2 extra css classes to field element -->
    {{ form.title|append_attr:"class:css_class_1 css_class_2" }}

'add_class' filter is just a shortcut for 'append_attr' filter that
adds values to the 'class' attribute.


remove_attr
-----------
Removes any single html attribute for the form field.

Example:

.. code-block:: html+django

    {% load widget_tweaks %}

    <!-- removes autofocus attribute from field element -->
    {{ form.title|remove_attr:"autofocus" }}


add_label_class
---------------

The same as `add_class` but adds css class to form labels.

Example:

.. code-block:: html+django

    {% load widget_tweaks %}

    <!-- add 2 extra css classes to field label element -->
    {{ form.title|add_label_class:"label_class_1 label_class_2" }}


add_error_class
---------------

The same as 'add_class' but adds css class only if validation failed for
the field (field.errors is not empty).

Example:

.. code-block:: html+django

    {% load widget_tweaks %}

    <!-- add 'error-border' css class on field error -->
    {{ form.title|add_error_class:"error-border" }}


add_error_attr
--------------

The same as 'attr' but sets an attribute only if validation failed for
the field (field.errors is not empty). This can be useful when dealing
with accessibility:

.. code-block:: html+django

    {% load widget_tweaks %}

    <!-- add aria-invalid="true" attribute on field error -->
    {{ form.title|add_error_attr:"aria-invalid:true" }}

add_required_class
------------------

The same as 'add_error_class' adds css class only for required field.

Example:

.. code-block:: html+django

    {% load widget_tweaks %}

    <!-- add 'is-required' css class on field required -->
    {{ form.title|add_required_class:"is-required" }}


field_type and widget_type
--------------------------

``'field_type'`` and ``'widget_type'`` are template filters that return
field class name and field widget class name (in lower case).

Example:

.. code-block:: html+django

    {% load widget_tweaks %}

    <div class="field {{ field|field_type }} {{ field|widget_type }} {{ field.html_name }}">
        {{ field }}
    </div>

Output:

.. code-block:: html+django

    <div class="field charfield textinput name">
        <input id="id_name" type="text" name="name" maxlength="100" />
    </div>


Mixing render_field and filters
===============================

The render_field tag and filters mentioned above can be mixed.

Example:

.. code-block:: html+django

    {% render_field form.category|append_attr:"readonly:readonly" type="text" placeholder="Category" %}


returns:

.. code-block:: html+django

    <input name="category" placeholder="Profession" readonly="readonly" type="text">


Filter chaining
===============

The order django-widget-tweaks filters apply may seem counter-intuitive
(leftmost filter wins):

.. code-block:: html+django

    {{ form.simple|attr:"foo:bar"|attr:"foo:baz" }}

returns:

.. code-block:: html+django

    <input foo="bar" type="text" name="simple" id="id_simple" />

It is not a bug, it is a feature that enables creating reusable templates
with overridable defaults.

Reusable field template example:

.. code-block:: html+django

    {# inc/field.html #}
    {% load widget_tweaks %}
    <div>{{ field|attr:"foo:default_foo" }}</div>

Example usage:

.. code-block:: html+django

    {# my_template.html #}
    {% load widget_tweaks %}
    <form method='POST' action=''> {% csrf_token %}
        {% include "inc/field.html" with field=form.title %}
        {% include "inc/field.html" with field=form.description|attr:"foo:non_default_foo" %}
    </form>

With 'rightmost filter wins' rule it wouldn't be possible to override
``|attr:"foo:default_foo"`` in main template.

Contributing
============

If you've found a bug, implemented a feature or have a suggestion,
do not hesitate to contact me, fire an issue or send a pull request.

* Source code: https://github.com/jazzband/django-widget-tweaks/
* Bug tracker: https://github.com/jazzband/django-widget-tweaks/issues

Testing
-------

Make sure you have `tox <http://tox.testrun.org/>`_ installed, then type

::

    tox

from the source checkout.

NOT SUPPORTED
=============

MultiWidgets: SplitDateTimeWidget, SplitHiddenDateTimeWidget


Changes
=======


1.5.0 (2023-08-25)
------------------

* Add Django 4.2 support.
* Add Django 4.1 support.
* Drop Django 4.0 support.
* Drop Django 2.2 support.
* Add Python 3.11 support.
* Drop Python 3.7 support.


1.4.12 (2022-01-13)
-------------------

* Set minimum required Python version to 3.7.
* Add better documentation syntax highlighting.
* Adjust build settings and stop building deprecated universal Python 2 wheels.


1.4.11 (2022-01-08)
-------------------

* Add support for Django 4.0
* Drop support for Django 3.0 and 3.1
* Add support for Python 3.10
* Drop support for Python 3.6


1.4.9 (2021-09-02)
------------------

* Add support for Django 3.2
* Move to GitHub Actions.
* Drop support for Django 1.11.
* Add support for Python 3.9.


1.4.8 (2020-03-12)
------------------

* Fix Release version


1.4.7 (2020-03-10)
------------------

* Fix Travis deployment to Jazzband site


1.4.6 (2020-03-09)
------------------

* Feature remove attribute from field
* Added documentation for remove_attr feature
* Reformat code with black for PEP8 compatibility 
* More consistent tox configuration
* adding a new templatetag, unittest and documentation
* Deprecate Python 2.7 support
* Use automatic formatting for all files


1.4.5 (2019-06-08)
------------------

* Fix rST formatting errors.


1.4.4 (2019-06-05)
------------------

* Add support for type attr.
* Add Python 3.7 and drop Python 3.3 support.
* Add support for double colon syntax.


1.4.3 (2018-09-6)
------------------

* Added add_label_class filter for CSS on form labels
* Removed compatibility code for unsupported Django versions
* Fixed support for non-value attributes in Django < 1.8
* Support non-value attributes in HTML5 by setting their value to True


1.4.2 (2018-03-19)
------------------

* update readme to make installation more clear
* shallow copy field before updating attributes
* drop Python 2.6 and Python 3.2 support
* always cast the result of render to a string
* fix import for django >= 2.0
* moved to jazzband


1.4.1 (2015-06-29)
------------------

* fixed a regression in django-widget-tweaks v1.4
  (the field is no longer deep copied).

1.4 (2015-06-27)
----------------

* Django 1.7, 1.8 and 1.9 support;
* setup.py is switched to setuptools;
* testing improvements;
* Python 3.4 support is added;
* Python 2.5 is not longer supported;
* bitbucket repository is no longer supported (development is moved to github).

1.3 (2013-04-05)
----------------

* added support for ``WIDGET_ERROR_CLASS`` and  ``WIDGET_REQUIRED_CLASS``
  template variables that affect ``{% render_field %}``.

1.2 (2013-03-23)
----------------

* new ``add_error_attr`` template filter;
* testing improvements.

1.1.2 (2012-06-06)
------------------

* support for template variables is added to ``render_field`` tag;
* new ``field_type`` and ``widget_type`` filters.

1.1.1 (2012-03-22)
------------------

* some issues with ``render_field`` tag are fixed.

1.1 (2012-03-22)
----------------

* ``render_field`` template tag.

1.0 (2012-02-06)
----------------

* filters return empty strings instead of raising exceptions if field is missing;
* test running improvements;
* python 3 support;
* undocumented 'behave' filter is removed.

0.3 (2011-03-04)
----------------

* ``add_error_class`` filter.

0.2.1 (2011-02-03)
------------------

* Attributes customized in widgets are preserved;
* no more extra whitespaces;
* tests;

0.1 (2011-01-12)
----------------

Initial release.
