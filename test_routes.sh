#!/bin/bash

# IMT Claims System Route Testing Script
echo "=== IMT Claims System Route Testing ==="
echo "Testing all routes systematically..."
echo ""

BASE_URL="http://127.0.0.1:8000"
COOKIE_FILE="test_cookies.txt"

# Function to test a route
test_route() {
    local route="$1"
    local description="$2"
    local expected_status="$3"
    local auth_required="$4"
    
    echo -n "Testing $description ($route): "
    
    if [ "$auth_required" = "true" ]; then
        status=$(curl -s -o /dev/null -w "%{http_code}" -b "$COOKIE_FILE" "$BASE_URL$route")
    else
        status=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$route")
    fi
    
    if [ "$status" = "$expected_status" ]; then
        echo "✅ $status"
    else
        echo "❌ $status (expected $expected_status)"
    fi
}

# Function to login
login_user() {
    local username="$1"
    local password="$2"
    
    echo "Logging in as $username..."
    
    # Get CSRF token
    csrf_token=$(curl -s -c "$COOKIE_FILE" "$BASE_URL/login/" | grep csrfmiddlewaretoken | sed -n 's/.*value="\([^"]*\)".*/\1/p' | head -1)
    
    # Login
    login_status=$(curl -s -o /dev/null -w "%{http_code}" -c "$COOKIE_FILE" -b "$COOKIE_FILE" \
        -d "username=$username&password=$password&csrfmiddlewaretoken=$csrf_token" \
        -X POST "$BASE_URL/login/")
    
    if [ "$login_status" = "302" ]; then
        echo "✅ Login successful"
        return 0
    else
        echo "❌ Login failed (status: $login_status)"
        return 1
    fi
}

# Clean up any existing cookies
rm -f "$COOKIE_FILE"

echo "=== 1. Testing Public Routes (Unauthenticated) ==="
test_route "/login/" "Login page" "200" "false"
test_route "/admin/" "Admin interface" "302" "false"

echo ""
echo "=== 2. Testing Protected Routes (Unauthenticated - Should Redirect) ==="
test_route "/" "Root dashboard" "302" "false"
test_route "/dashboard/" "Dashboard alternative" "302" "false"
test_route "/submit/" "Submit claim" "302" "false"
test_route "/list/" "Claims list" "302" "false"
test_route "/my-claims/" "My claims" "302" "false"
test_route "/tasks/" "Tasks" "302" "false"

echo ""
echo "=== 3. Testing with Customer Authentication ==="
if login_user "customer" "password123"; then
    test_route "/" "Root dashboard" "200" "true"
    test_route "/dashboard/" "Dashboard alternative" "200" "true"
    test_route "/submit/" "Submit claim" "200" "true"
    test_route "/my-claims/" "My claims" "200" "true"
    test_route "/list/" "Claims list (should be forbidden)" "403" "true"
    test_route "/tasks/" "Tasks (should be forbidden)" "403" "true"
fi

# Logout
curl -s -o /dev/null -b "$COOKIE_FILE" "$BASE_URL/logout/"
rm -f "$COOKIE_FILE"

echo ""
echo "=== 4. Testing with Staff Authentication (Adjuster) ==="
if login_user "adjuster" "password123"; then
    test_route "/" "Root dashboard" "200" "true"
    test_route "/dashboard/" "Dashboard alternative" "200" "true"
    test_route "/submit/" "Submit claim" "200" "true"
    test_route "/list/" "Claims list" "200" "true"
    test_route "/my-claims/" "My claims" "200" "true"
    test_route "/tasks/" "Tasks" "200" "true"
fi

# Logout
curl -s -o /dev/null -b "$COOKIE_FILE" "$BASE_URL/logout/"
rm -f "$COOKIE_FILE"

echo ""
echo "=== 5. Testing Logout ==="
test_route "/logout/" "Logout" "302" "false"

echo ""
echo "=== 6. Testing Static Files ==="
test_route "/static/css/imt-style.css" "CSS file" "200" "false"

echo ""
echo "=== Route Testing Complete ==="

# Clean up
rm -f "$COOKIE_FILE"
