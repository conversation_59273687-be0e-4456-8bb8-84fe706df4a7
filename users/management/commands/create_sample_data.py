from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from claims.models import Claim, ClaimNote, Task
from users.models import UserProfile

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample claims and data for testing'

    def handle(self, *args, **options):
        # Get users
        try:
            customer = User.objects.get(username='customer')
            adjuster = User.objects.get(username='adjuster')
            admin_user = User.objects.get(username='admin')
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Please run create_sample_users first')
            )
            return

        # Create sample claims
        claims_data = [
            {
                'claim_type': 'auto',
                'status': 'under_review',
                'priority': 'high',
                'incident_date': timezone.now() - timedelta(days=5),
                'incident_location': '123 Main Street, Des Moines, IA 50309',
                'incident_description': 'Rear-end collision at intersection. Other driver ran red light and hit my vehicle from behind. Significant damage to rear bumper and trunk area.',
                'estimated_damage_amount': 4500.00,
                'deductible_amount': 500.00,
            },
            {
                'claim_type': 'home',
                'status': 'investigating',
                'priority': 'medium',
                'incident_date': timezone.now() - timedelta(days=12),
                'incident_location': '456 Oak Avenue, West Des Moines, IA 50266',
                'incident_description': 'Water damage in basement due to burst pipe. Affected area includes finished basement, carpet, drywall, and some personal belongings.',
                'estimated_damage_amount': 8200.00,
                'deductible_amount': 1000.00,
            },
            {
                'claim_type': 'auto',
                'status': 'approved',
                'priority': 'low',
                'incident_date': timezone.now() - timedelta(days=25),
                'incident_location': '789 Elm Street, Ankeny, IA 50023',
                'incident_description': 'Hail damage to vehicle during severe storm. Multiple dents on hood, roof, and trunk. No glass damage.',
                'estimated_damage_amount': 2800.00,
                'approved_amount': 2300.00,
                'deductible_amount': 500.00,
            }
        ]

        created_claims = []
        for claim_data in claims_data:
            claim, created = Claim.objects.get_or_create(
                claimant=customer,
                incident_location=claim_data['incident_location'],
                defaults={
                    **claim_data,
                    'assigned_adjuster': adjuster,
                }
            )
            if created:
                created_claims.append(claim)
                self.stdout.write(f'Created claim: {claim.claim_number}')

        # Create sample notes for claims
        if created_claims:
            for claim in created_claims:
                # Customer note
                ClaimNote.objects.get_or_create(
                    claim=claim,
                    author=customer,
                    defaults={
                        'note': 'I have submitted all the required documentation. Please let me know if you need anything else.',
                        'is_internal': False,
                    }
                )
                
                # Adjuster note
                ClaimNote.objects.get_or_create(
                    claim=claim,
                    author=adjuster,
                    defaults={
                        'note': 'Initial review completed. Claim appears valid. Proceeding with investigation.',
                        'is_internal': True,
                    }
                )

        # Create sample tasks
        if created_claims:
            for claim in created_claims[:2]:  # Only for first 2 claims
                Task.objects.get_or_create(
                    claim=claim,
                    assigned_to=adjuster,
                    created_by=admin_user,
                    defaults={
                        'title': f'Review documentation for {claim.claim_number}',
                        'description': 'Review all submitted documents and verify claim details.',
                        'priority': 'medium',
                        'due_date': timezone.now() + timedelta(days=3),
                    }
                )

        self.stdout.write(
            self.style.SUCCESS('Sample data created successfully!')
        )
        self.stdout.write('You can now log in and test the system with sample claims.')
