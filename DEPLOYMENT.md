# IMT Insurance Claims System - Production Deployment Guide

This guide provides comprehensive instructions for deploying the IMT Insurance Claims System in production environments using Docker and Kubernetes.

## 🏗️ Architecture Overview

The production deployment includes:

- **Django Application**: Main web application with gunicorn WSGI server
- **PostgreSQL**: Production database with persistent storage
- **Redis**: Caching and session storage
- **Nginx**: Reverse proxy, SSL termination, and static file serving
- **Celery**: Background task processing (optional)

## 🚀 Quick Start

### Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- Kubernetes 1.20+ (for K8s deployment)
- kubectl (for K8s deployment)

### 1. <PERSON>lone and Setup

```bash
git clone <repository-url>
cd demo-claims
cp .env.example .env
# Edit .env with your configuration
```

### 2. Deploy with Docker Compose

```bash
./deploy.sh docker
```

### 3. Deploy to Kubernetes

```bash
./deploy.sh k8s
```

## 📋 Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Django Settings
SECRET_KEY=your-super-secret-key-here
DEBUG=False
ALLOWED_HOSTS=demo.imtins.com,www.demo.imtins.com

# Database
DB_PASSWORD=your-secure-database-password
REDIS_PASSWORD=your-secure-redis-password

# Email (optional)
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
```

### SSL Certificates

For production, place your SSL certificates in:
- `nginx/ssl/demo.imtins.com.crt`
- `nginx/ssl/demo.imtins.com.key`
- `nginx/ssl/ca-bundle.crt` (optional)
- `nginx/ssl/dhparam.pem` (generate with: `openssl dhparam -out dhparam.pem 2048`)

## 🐳 Docker Deployment

### Development

```bash
docker-compose up -d
```

### Production

```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Services

- **Web Application**: http://localhost:8000
- **Nginx Proxy**: http://localhost (redirects to HTTPS)
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

### Management Commands

```bash
# View logs
docker-compose logs -f web

# Run Django commands
docker-compose exec web python manage.py migrate
docker-compose exec web python manage.py createsuperuser
docker-compose exec web python manage.py collectstatic

# Database backup
docker-compose exec postgres pg_dump -U claims_user claims_db > backup.sql

# Stop services
docker-compose down
```

## ☸️ Kubernetes Deployment

### Prerequisites

1. Kubernetes cluster with ingress controller
2. cert-manager for SSL certificates (optional)
3. Storage classes: `fast-ssd`, `shared-storage`

### Deployment Steps

1. **Build and push image**:
```bash
docker build -t your-registry/imt-claims:latest .
docker push your-registry/imt-claims:latest
```

2. **Update image references** in `k8s/django-deployment.yaml`

3. **Deploy**:
```bash
kubectl apply -f k8s/
```

### Kubernetes Resources

- **Namespace**: `imt-claims`
- **Deployments**: django, postgres, redis, nginx
- **Services**: Internal cluster communication
- **Ingress**: External access with SSL
- **PersistentVolumes**: Database and media storage
- **ConfigMaps**: Application configuration
- **Secrets**: Sensitive data

### Management Commands

```bash
# Check status
kubectl get pods -n imt-claims

# View logs
kubectl logs -f deployment/django-deployment -n imt-claims

# Run Django commands
kubectl exec -it deployment/django-deployment -n imt-claims -- python manage.py migrate

# Scale application
kubectl scale deployment django-deployment --replicas=5 -n imt-claims

# Update deployment
kubectl set image deployment/django-deployment django=your-registry/imt-claims:v2 -n imt-claims
```

## 🔒 Security Considerations

### Production Settings

- `DEBUG=False`
- Strong `SECRET_KEY`
- Secure database passwords
- SSL/TLS encryption
- Security headers via Nginx
- Rate limiting
- CSRF protection

### Network Security

- Private container networks
- Database not exposed externally
- Nginx as the only public-facing service
- Security headers and CSP policies

### Data Protection

- Encrypted database connections
- Secure session handling
- File upload restrictions
- Input validation and sanitization

## 📊 Monitoring and Health Checks

### Health Endpoints

- `/health/` - Overall application health
- `/ready/` - Kubernetes readiness probe
- `/alive/` - Kubernetes liveness probe

### Monitoring

The application includes:
- Database connectivity checks
- Cache availability checks
- Custom health metrics
- Structured logging

### Logs

Logs are available at:
- Docker: `docker-compose logs`
- Kubernetes: `kubectl logs`
- Application logs: `/app/logs/django.log`

## 🔄 Backup and Recovery

### Database Backup

```bash
# Docker
docker-compose exec postgres pg_dump -U claims_user claims_db > backup.sql

# Kubernetes
kubectl exec -it deployment/postgres-deployment -n imt-claims -- pg_dump -U claims_user claims_db > backup.sql
```

### Media Files Backup

```bash
# Docker
docker cp container_name:/app/media ./media_backup

# Kubernetes
kubectl cp imt-claims/pod-name:/app/media ./media_backup
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials
   - Verify network connectivity
   - Check database service status

2. **Static Files Not Loading**
   - Run `collectstatic` command
   - Check Nginx configuration
   - Verify volume mounts

3. **SSL Certificate Issues**
   - Verify certificate files
   - Check certificate validity
   - Update Nginx SSL configuration

### Debug Commands

```bash
# Check container status
docker-compose ps
kubectl get pods -n imt-claims

# View detailed logs
docker-compose logs web
kubectl describe pod <pod-name> -n imt-claims

# Test database connection
docker-compose exec web python manage.py dbshell
```

## 📈 Performance Optimization

### Scaling

- **Horizontal**: Increase replica count
- **Vertical**: Increase resource limits
- **Database**: Use read replicas
- **Cache**: Implement Redis clustering

### Resource Limits

Recommended production limits:
- Django: 1GB RAM, 0.5 CPU
- PostgreSQL: 2GB RAM, 1 CPU
- Redis: 512MB RAM, 0.25 CPU
- Nginx: 256MB RAM, 0.25 CPU

## 🔧 Maintenance

### Updates

1. Build new image with updated code
2. Update deployment with new image
3. Monitor rollout status
4. Rollback if issues occur

### Database Migrations

```bash
# Docker
docker-compose exec web python manage.py migrate

# Kubernetes
kubectl exec -it deployment/django-deployment -n imt-claims -- python manage.py migrate
```

## 📞 Support

For deployment issues:
1. Check logs for error messages
2. Verify configuration settings
3. Test individual components
4. Review security settings
5. Contact system administrator

---

**IMT Insurance Claims System** - Production-ready deployment with enterprise-grade security and scalability.
