#!/usr/bin/env python
"""
Django Claims System Route Testing Script
Tests all routes systematically with proper authentication
"""

import os
import sys
import django
from django.test import Client
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'claims_system.settings')
django.setup()

User = get_user_model()

def test_routes():
    """Test all routes systematically"""
    client = Client()
    
    print("=== IMT Claims System Route Testing ===")
    print("Testing all routes systematically...\n")
    
    # Test data
    routes = {
        'public': [
            ('/login/', 'Login page'),
            ('/admin/', 'Admin interface'),
        ],
        'protected_unauthenticated': [
            ('/', 'Root dashboard'),
            ('/dashboard/', 'Dashboard alternative'),
            ('/submit/', 'Submit claim'),
            ('/list/', 'Claims list'),
            ('/my-claims/', 'My claims'),
            ('/tasks/', 'Tasks'),
        ],
        'customer_routes': [
            ('/', 'Root dashboard'),
            ('/dashboard/', 'Dashboard alternative'),
            ('/submit/', 'Submit claim'),
            ('/my-claims/', 'My claims'),
        ],
        'staff_routes': [
            ('/', 'Root dashboard'),
            ('/dashboard/', 'Dashboard alternative'),
            ('/submit/', 'Submit claim'),
            ('/list/', 'Claims list'),
            ('/my-claims/', 'My claims'),
            ('/tasks/', 'Tasks'),
        ],
        'static': [
            ('/static/css/imt-style.css', 'CSS file'),
        ]
    }
    
    def test_route_group(routes_list, description, expected_status=200, client_instance=None):
        """Test a group of routes"""
        if client_instance is None:
            client_instance = client
            
        print(f"=== {description} ===")
        for route, name in routes_list:
            try:
                response = client_instance.get(route)
                status = response.status_code
                
                if status == expected_status:
                    print(f"✅ {name} ({route}): {status}")
                elif status == 302 and expected_status == 200:
                    print(f"🔄 {name} ({route}): {status} (redirect)")
                elif status == 403 and 'list' in route or 'tasks' in route:
                    print(f"🔒 {name} ({route}): {status} (forbidden - correct for customer)")
                else:
                    print(f"❌ {name} ({route}): {status} (expected {expected_status})")
                    
            except Exception as e:
                print(f"❌ {name} ({route}): Error - {str(e)}")
        print()
    
    # 1. Test public routes
    test_route_group(routes['public'], "Public Routes", 200)
    
    # 2. Test protected routes without authentication (should redirect)
    test_route_group(routes['protected_unauthenticated'], "Protected Routes (Unauthenticated)", 302)
    
    # 3. Test with customer authentication
    try:
        customer = User.objects.get(username='customer')
        client.force_login(customer)
        test_route_group(routes['customer_routes'], "Customer Routes (Authenticated)", 200, client)
        
        # Test forbidden routes for customer
        print("=== Customer Forbidden Routes ===")
        for route, name in [('/list/', 'Claims list'), ('/tasks/', 'Tasks')]:
            try:
                response = client.get(route)
                status = response.status_code
                if status == 403:
                    print(f"🔒 {name} ({route}): {status} (correctly forbidden)")
                elif status == 200:
                    print(f"⚠️  {name} ({route}): {status} (should be forbidden)")
                else:
                    print(f"❓ {name} ({route}): {status}")
            except Exception as e:
                print(f"❌ {name} ({route}): Error - {str(e)}")
        print()
        
        client.logout()
    except User.DoesNotExist:
        print("❌ Customer user not found")
    
    # 4. Test with staff authentication
    try:
        adjuster = User.objects.get(username='adjuster')
        client.force_login(adjuster)
        test_route_group(routes['staff_routes'], "Staff Routes (Authenticated)", 200, client)
        client.logout()
    except User.DoesNotExist:
        print("❌ Adjuster user not found")
    
    # 5. Test static files
    test_route_group(routes['static'], "Static Files", 200)
    
    # 6. Test logout
    print("=== Logout Test ===")
    response = client.get('/logout/')
    if response.status_code == 302:
        print("✅ Logout: 302 (redirect)")
    else:
        print(f"❌ Logout: {response.status_code}")
    print()
    
    # 7. Test specific claim routes (if claims exist)
    try:
        from claims.models import Claim
        if Claim.objects.exists():
            claim = Claim.objects.first()
            claim_routes = [
                (f'/claim/{claim.id}/', 'Claim detail'),
                (f'/claim/{claim.id}/edit/', 'Claim edit'),
                (f'/claim/{claim.id}/add-note/', 'Add note'),
                (f'/claim/{claim.id}/add-task/', 'Add task'),
            ]
            
            # Test with staff user
            adjuster = User.objects.get(username='adjuster')
            client.force_login(adjuster)
            
            print("=== Specific Claim Routes ===")
            for route, name in claim_routes:
                try:
                    response = client.get(route)
                    status = response.status_code
                    if status in [200, 302]:
                        print(f"✅ {name} ({route}): {status}")
                    else:
                        print(f"❌ {name} ({route}): {status}")
                except Exception as e:
                    print(f"❌ {name} ({route}): Error - {str(e)}")
            print()
            
            client.logout()
    except Exception as e:
        print(f"⚠️  Could not test specific claim routes: {str(e)}")
    
    print("=== Route Testing Complete ===")

if __name__ == '__main__':
    test_routes()
