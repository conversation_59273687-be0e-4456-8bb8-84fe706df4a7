from django.db import models
from django.conf import settings
from decimal import Decimal


class Payment(models.Model):
    """Payment model for claim settlements"""

    PAYMENT_STATUS = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    )

    PAYMENT_METHOD = (
        ('check', 'Check'),
        ('direct_deposit', 'Direct Deposit'),
        ('wire_transfer', 'Wire Transfer'),
    )

    claim = models.ForeignKey('claims.Claim', on_delete=models.CASCADE, related_name='payments')
    payee = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='received_payments')
    processed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_payments'
    )

    amount = models.DecimalField(max_digits=12, decimal_places=2)
    payment_method = models.Char<PERSON>ield(max_length=20, choices=PAYMENT_METHOD, default='check')
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS, default='pending')

    reference_number = models.CharField(max_length=50, blank=True)
    notes = models.TextField(blank=True)

    # Banking information (for direct deposits)
    bank_name = models.CharField(max_length=100, blank=True)
    account_number = models.CharField(max_length=50, blank=True)
    routing_number = models.CharField(max_length=20, blank=True)

    # Mailing information (for checks)
    mailing_address = models.TextField(blank=True)

    scheduled_date = models.DateField(null=True, blank=True)
    processed_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Payment ${self.amount} for {self.claim.claim_number}"


class PaymentHistory(models.Model):
    """Track payment status changes"""
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='history')
    status = models.CharField(max_length=20, choices=Payment.PAYMENT_STATUS)
    notes = models.TextField(blank=True)
    changed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = 'Payment histories'

    def __str__(self):
        return f"Payment {self.payment.id} - {self.status}"


class Settlement(models.Model):
    """Settlement details for claims"""
    claim = models.OneToOneField('claims.Claim', on_delete=models.CASCADE, related_name='settlement')
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    deductible_applied = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    net_amount = models.DecimalField(max_digits=12, decimal_places=2)

    settlement_date = models.DateField()
    settlement_notes = models.TextField(blank=True)

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='approved_settlements'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        self.net_amount = self.total_amount - self.deductible_applied
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Settlement for {self.claim.claim_number} - ${self.net_amount}"
