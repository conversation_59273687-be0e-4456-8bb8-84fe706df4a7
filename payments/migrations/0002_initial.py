# Generated by Django 4.2.21 on 2025-05-28 19:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('claims', '0002_initial'),
        ('payments', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='settlement',
            name='approved_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_settlements', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='settlement',
            name='claim',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='settlement', to='claims.claim'),
        ),
        migrations.AddField(
            model_name='paymenthistory',
            name='changed_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='paymenthistory',
            name='payment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='payments.payment'),
        ),
        migrations.AddField(
            model_name='payment',
            name='claim',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='claims.claim'),
        ),
        migrations.AddField(
            model_name='payment',
            name='payee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_payments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='payment',
            name='processed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_payments', to=settings.AUTH_USER_MODEL),
        ),
    ]
