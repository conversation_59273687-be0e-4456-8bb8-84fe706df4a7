# IMT Insurance Claims System - Environment Variables Template
# Copy this file to .env and update the values for your environment

# Django Settings
SECRET_KEY=your-super-secret-key-here-change-this-in-production
DEBUG=False
DJANGO_SETTINGS_MODULE=claims_system.settings.production

# Domain Configuration
ALLOWED_HOSTS=demo.imtins.com,www.demo.imtins.com,localhost,127.0.0.1

# Database Configuration
DB_ENGINE=django.db.backends.postgresql
DB_NAME=claims_db
DB_USER=claims_user
DB_PASSWORD=your-database-password-here
DB_HOST=postgres
DB_PORT=5432

# Redis Configuration
REDIS_URL=redis://:your-redis-password@redis:6379/1
REDIS_PASSWORD=your-redis-password-here

# Celery Configuration
CELERY_BROKER_URL=redis://:your-redis-password@redis:6379/0
CELERY_RESULT_BACKEND=redis://:your-redis-password@redis:6379/0

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Security Settings (for production)
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
