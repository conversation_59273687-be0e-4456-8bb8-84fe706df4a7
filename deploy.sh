#!/bin/bash

# IMT Insurance Claims System - Deployment Script
# This script helps deploy the application using Docker Compose or Kubernetes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="imt-claims"
IMAGE_TAG="latest"
NAMESPACE="imt-claims"

# Functions
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}  IMT Insurance Claims System Deployment${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Build Docker image
build_image() {
    print_info "Building Docker image..."
    docker build -t ${IMAGE_NAME}:${IMAGE_TAG} .
    print_success "Docker image built successfully"
}

# Deploy with Docker Compose
deploy_docker_compose() {
    print_info "Deploying with Docker Compose..."
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from template..."
        cp .env.example .env
        print_warning "Please update .env file with your configuration before running again"
        exit 1
    fi
    
    # Build and start services
    docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build
    
    print_success "Application deployed with Docker Compose"
    print_info "Application will be available at:"
    print_info "  - HTTP: http://localhost"
    print_info "  - HTTPS: https://localhost (if SSL is configured)"
}

# Deploy to Kubernetes
deploy_kubernetes() {
    print_info "Deploying to Kubernetes..."
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed"
        exit 1
    fi
    
    # Create namespace
    kubectl apply -f k8s/namespace.yaml
    
    # Apply configurations
    kubectl apply -f k8s/persistent-volumes.yaml
    kubectl apply -f k8s/configmap.yaml
    kubectl apply -f k8s/secrets.yaml
    
    # Deploy services
    kubectl apply -f k8s/postgres-deployment.yaml
    kubectl apply -f k8s/postgres-service.yaml
    kubectl apply -f k8s/redis-deployment.yaml
    kubectl apply -f k8s/django-deployment.yaml
    kubectl apply -f k8s/django-service.yaml
    kubectl apply -f k8s/nginx-deployment.yaml
    kubectl apply -f k8s/nginx-service.yaml
    kubectl apply -f k8s/ingress.yaml
    
    print_success "Application deployed to Kubernetes"
    print_info "Checking deployment status..."
    kubectl get pods -n ${NAMESPACE}
}

# Show logs
show_logs() {
    if [ "$1" = "k8s" ]; then
        kubectl logs -f deployment/django-deployment -n ${NAMESPACE}
    else
        docker compose logs -f web
    fi
}

# Stop services
stop_services() {
    if [ "$1" = "k8s" ]; then
        kubectl delete namespace ${NAMESPACE}
    else
        docker-compose down
    fi
    print_success "Services stopped"
}

# Main script
print_header

case "$1" in
    "docker")
        check_prerequisites
        build_image
        deploy_docker_compose
        ;;
    "k8s"|"kubernetes")
        check_prerequisites
        build_image
        deploy_kubernetes
        ;;
    "logs")
        show_logs $2
        ;;
    "stop")
        stop_services $2
        ;;
    "build")
        check_prerequisites
        build_image
        ;;
    *)
        echo "Usage: $0 {docker|k8s|logs|stop|build}"
        echo ""
        echo "Commands:"
        echo "  docker     - Deploy using Docker Compose"
        echo "  k8s        - Deploy to Kubernetes"
        echo "  logs       - Show application logs (add 'k8s' for Kubernetes logs)"
        echo "  stop       - Stop services (add 'k8s' for Kubernetes)"
        echo "  build      - Build Docker image only"
        echo ""
        echo "Examples:"
        echo "  $0 docker          # Deploy with Docker Compose"
        echo "  $0 k8s             # Deploy to Kubernetes"
        echo "  $0 logs            # Show Docker Compose logs"
        echo "  $0 logs k8s        # Show Kubernetes logs"
        echo "  $0 stop            # Stop Docker Compose services"
        echo "  $0 stop k8s        # Delete Kubernetes deployment"
        exit 1
        ;;
esac
