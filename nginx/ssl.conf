# SSL Configuration for IMT Insurance Claims System

# SSL certificates (replace with your actual certificates)
ssl_certificate /etc/nginx/ssl/demo.imtins.com.crt;
ssl_certificate_key /etc/nginx/ssl/demo.imtins.com.key;

# SSL protocols and ciphers
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA:ECDHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA;
ssl_prefer_server_ciphers off;

# SSL session settings
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_session_tickets off;

# OCSP stapling
ssl_stapling on;
ssl_stapling_verify on;
ssl_trusted_certificate /etc/nginx/ssl/ca-bundle.crt;

# Security headers for SSL
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# DH parameters for perfect forward secrecy
ssl_dhparam /etc/nginx/ssl/dhparam.pem;
