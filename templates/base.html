<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}IMT Insurance Claims System{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    {% load static %}
    <link href="{% static 'css/imt-style.css' %}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="{% url 'claims:dashboard' %}">
                <i class="fas fa-shield-alt me-2"></i>IMT Insurance Claims
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'claims:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                        {% if user.user_type == 'customer' %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'claims:submit' %}">
                                    <i class="fas fa-plus me-1"></i>Submit Claim
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'claims:my_claims' %}">
                                    <i class="fas fa-list me-1"></i>My Claims
                                </a>
                            </li>
                        {% elif user.user_type == 'adjuster' or user.user_type == 'admin' %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'claims:list' %}">
                                    <i class="fas fa-clipboard-list me-1"></i>All Claims
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'claims:tasks' %}">
                                    <i class="fas fa-tasks me-1"></i>Tasks
                                </a>
                            </li>
                        {% endif %}
                        {% if user.user_type == 'admin' %}
                            <li class="nav-item">
                                <a class="nav-link" href="/admin/">
                                    <i class="fas fa-cog me-1"></i>Admin
                                </a>
                            </li>
                        {% endif %}
                    {% endif %}
                </ul>

                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ user.full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'users:profile' %}">
                                    <i class="fas fa-user-edit me-1"></i>Profile
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'logout' %}">
                                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>IMT Insurance Claims System</h5>
                    <p class="mb-0">Secure, efficient claims processing for IMT Insurance customers.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <strong>Corporate Office:</strong> 7825 Mills Civic Parkway, West Des Moines, IA 50266<br>
                        <strong>Phone:</strong> <a href="tel:+18002743531">800‑274‑3531</a>
                    </p>
                </div>
            </div>
            <hr class="my-3">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">&copy; {% now "Y" %} IMT Insurance. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
