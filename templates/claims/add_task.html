{% extends 'base.html' %}
{% load static %}

{% block title %}Add Task - Claim {{ claim.claim_number }} - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'claims:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'claims:detail' claim.id %}">{{ claim.claim_number }}</a></li>
                <li class="breadcrumb-item active">Add Task</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-plus me-2"></i>Add Task to Claim {{ claim.claim_number }}
                </h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">Task Title</label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.title.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.description.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.assigned_to.id_for_label }}" class="form-label">Assign To</label>
                                {{ form.assigned_to }}
                                {% if form.assigned_to.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.assigned_to.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.priority.id_for_label }}" class="form-label">Priority</label>
                                {{ form.priority }}
                                {% if form.priority.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.priority.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.due_date.id_for_label }}" class="form-label">Due Date (Optional)</label>
                        {{ form.due_date }}
                        {% if form.due_date.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.due_date.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'claims:detail' claim.id %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Task
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Claim Information
                </h6>
            </div>
            <div class="card-body">
                <p><strong>Claim Number:</strong> {{ claim.claim_number }}</p>
                <p><strong>Customer:</strong> {{ claim.claimant.full_name }}</p>
                <p><strong>Type:</strong> {{ claim.get_claim_type_display }}</p>
                <p><strong>Status:</strong> 
                    <span class="badge status-{{ claim.status }}">{{ claim.get_status_display }}</span>
                </p>
                <p class="mb-0"><strong>Priority:</strong> 
                    <span class="badge priority-{{ claim.priority }}">{{ claim.get_priority_display }}</span>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
