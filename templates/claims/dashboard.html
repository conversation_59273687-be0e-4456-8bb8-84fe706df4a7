{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
            <small class="text-muted">Welcome, {{ user.full_name|default:user.username }}</small>
        </h1>
    </div>
</div>

{% if user.user_type == 'customer' %}
<!-- Customer Dashboard -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card stat-card">
            <div class="card-body">
                <div class="stat-number">{{ total_claims }}</div>
                <div class="stat-label">Total Claims</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card stat-card">
            <div class="card-body">
                <div class="stat-number">{{ pending_claims }}</div>
                <div class="stat-label">Pending Claims</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card stat-card">
            <div class="card-body">
                <div class="stat-number">{{ approved_claims }}</div>
                <div class="stat-label">Approved Claims</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <a href="{% url 'claims:submit' %}" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Submit New Claim
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Recent Claims
                </h5>
            </div>
            <div class="card-body">
                {% if recent_claims %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Claim Number</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Date Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for claim in recent_claims %}
                                <tr>
                                    <td><strong>{{ claim.claim_number }}</strong></td>
                                    <td>{{ claim.get_claim_type_display }}</td>
                                    <td>
                                        <span class="badge status-{{ claim.status }}">
                                            {{ claim.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ claim.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <a href="{% url 'claims:detail' claim.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'claims:my_claims' %}" class="btn btn-outline-primary">
                            View All My Claims
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <h5>No Claims Yet</h5>
                        <p class="text-muted">You haven't submitted any claims yet.</p>
                        <a href="{% url 'claims:submit' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Submit Your First Claim
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% elif user.user_type in 'adjuster,admin' %}
<!-- Adjuster/Admin Dashboard -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card stat-card">
            <div class="card-body">
                <div class="stat-number">{{ total_claims }}</div>
                <div class="stat-label">Total Claims</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card stat-card">
            <div class="card-body">
                <div class="stat-number">{{ assigned_claims }}</div>
                <div class="stat-label">Assigned to Me</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card stat-card">
            <div class="card-body">
                <div class="stat-number">{{ pending_tasks }}</div>
                <div class="stat-label">Pending Tasks</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <a href="{% url 'claims:list' %}" class="btn btn-primary btn-lg">
                    <i class="fas fa-clipboard-list me-2"></i>View All Claims
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Urgent Claims
                </h5>
            </div>
            <div class="card-body">
                {% if urgent_claims %}
                    {% for claim in urgent_claims %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <strong>{{ claim.claim_number }}</strong><br>
                            <small class="text-muted">{{ claim.claimant.full_name }}</small>
                        </div>
                        <div>
                            <span class="badge priority-urgent">Urgent</span>
                            <a href="{% url 'claims:detail' claim.id %}" class="btn btn-sm btn-outline-primary ms-2">
                                View
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">No urgent claims</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>Pending Tasks
                </h5>
            </div>
            <div class="card-body">
                {% if pending_tasks_list %}
                    {% for task in pending_tasks_list %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <strong>{{ task.title }}</strong><br>
                            <small class="text-muted">{{ task.claim.claim_number }}</small>
                        </div>
                        <div>
                            {% if task.due_date %}
                                <small class="text-muted">Due: {{ task.due_date|date:"M d" }}</small>
                            {% endif %}
                            <a href="{% url 'claims:task_detail' task.id %}" class="btn btn-sm btn-outline-primary ms-2">
                                View
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                    <div class="text-center mt-3">
                        <a href="{% url 'claims:tasks' %}" class="btn btn-outline-primary">
                            View All Tasks
                        </a>
                    </div>
                {% else %}
                    <p class="text-muted text-center">No pending tasks</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Claims
                </h5>
            </div>
            <div class="card-body">
                {% if recent_claims %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Claim Number</th>
                                    <th>Customer</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for claim in recent_claims %}
                                <tr>
                                    <td><strong>{{ claim.claim_number }}</strong></td>
                                    <td>{{ claim.claimant.full_name }}</td>
                                    <td>{{ claim.get_claim_type_display }}</td>
                                    <td>
                                        <span class="badge status-{{ claim.status }}">
                                            {{ claim.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge priority-{{ claim.priority }}">
                                            {{ claim.get_priority_display }}
                                        </span>
                                    </td>
                                    <td>{{ claim.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <a href="{% url 'claims:detail' claim.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">No recent claims</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
