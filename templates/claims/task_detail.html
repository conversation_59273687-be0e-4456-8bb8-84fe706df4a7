{% extends 'base.html' %}
{% load static %}

{% block title %}Task: {{ task.title }} - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'claims:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'claims:tasks' %}">Tasks</a></li>
                <li class="breadcrumb-item active">{{ task.title|truncatechars:30 }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>{{ task.title }}
                </h4>
                <div>
                    <span class="badge bg-{% if task.status == 'completed' %}success{% elif task.status == 'in_progress' %}warning{% else %}secondary{% endif %} me-2">
                        {{ task.get_status_display }}
                    </span>
                    <span class="badge priority-{{ task.priority }}">{{ task.get_priority_display }}</span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle me-1"></i>Task Information</h6>
                        <p class="mb-1"><strong>Assigned to:</strong> {{ task.assigned_to.full_name }}</p>
                        <p class="mb-1"><strong>Created by:</strong> {{ task.created_by.full_name }}</p>
                        <p class="mb-1"><strong>Created:</strong> {{ task.created_at|date:"M d, Y g:i A" }}</p>
                        {% if task.due_date %}
                            <p class="mb-3"><strong>Due Date:</strong> {{ task.due_date|date:"M d, Y g:i A" }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-clipboard-list me-1"></i>Related Claim</h6>
                        <p class="mb-1"><strong>Claim Number:</strong> 
                            <a href="{% url 'claims:detail' task.claim.id %}">{{ task.claim.claim_number }}</a>
                        </p>
                        <p class="mb-1"><strong>Customer:</strong> {{ task.claim.claimant.full_name }}</p>
                        <p class="mb-3"><strong>Claim Status:</strong> 
                            <span class="badge status-{{ task.claim.status }}">{{ task.claim.get_status_display }}</span>
                        </p>
                    </div>
                </div>
                
                <hr>
                
                <h6><i class="fas fa-file-alt me-1"></i>Description</h6>
                <p class="mb-0">{{ task.description|linebreaks }}</p>
                
                {% if task.completed_at %}
                    <hr>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Task Completed:</strong> {{ task.completed_at|date:"M d, Y g:i A" }}
                    </div>
                {% endif %}
            </div>
            {% if task.status != 'completed' and task.assigned_to == user %}
                <div class="card-footer">
                    <form method="post" action="{% url 'claims:complete_task' task.id %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-success" onclick="return confirm('Mark this task as completed?')">
                            <i class="fas fa-check me-2"></i>Mark as Completed
                        </button>
                    </form>
                </div>
            {% endif %}
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'claims:detail' task.claim.id %}" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>View Claim
                    </a>
                    <a href="{% url 'claims:tasks' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Tasks
                    </a>
                </div>
            </div>
        </div>
        
        {% if task.due_date %}
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Due Date
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>Due:</strong> {{ task.due_date|date:"M d, Y g:i A" }}</p>
                    {% if task.due_date < today and task.status != 'completed' %}
                        <div class="alert alert-danger mb-0">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>Overdue!</strong>
                        </div>
                    {% elif task.due_date|timeuntil %}
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-1"></i>
                            Due in {{ task.due_date|timeuntil }}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
