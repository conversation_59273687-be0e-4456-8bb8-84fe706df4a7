{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Login - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>IMT Insurance
                </h4>
                <p class="text-muted mb-0">Claims System Login</p>
            </div>
            <div class="card-body">
                {% if form.errors %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Please correct the errors below.
                    </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user me-1"></i>Username
                        </label>
                        <input type="text" name="username" class="form-control" id="{{ form.username.id_for_label }}" required>
                        {% if form.username.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.username.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <input type="password" name="password" class="form-control" id="{{ form.password.id_for_label }}" required>
                        {% if form.password.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.password.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <small class="text-muted">
                    Need help? Contact your IMT agent or call
                    <a href="tel:+***********">800‑274‑3531</a>
                </small>
            </div>
        </div>

        <!-- Demo Accounts Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Demo Accounts
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <strong>Admin Account:</strong><br>
                        <small>Username: admin<br>Password: admin123</small>
                    </div>
                    <div class="col-6">
                        <strong>Customer Account:</strong><br>
                        <small>Username: customer<br>Password: customer123</small>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <strong>Adjuster Account:</strong><br>
                        <small>Username: adjuster<br>Password: adjuster123</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
