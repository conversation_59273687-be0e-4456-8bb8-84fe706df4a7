{% extends 'base.html' %}
{% load static %}

{% block title %}Profile - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'claims:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Profile</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-user me-2"></i>User Profile
                </h4>
                <a href="{% url 'users:edit_profile' %}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>Edit Profile
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        {% if profile.avatar %}
                            <img src="{{ profile.avatar.url }}" alt="Avatar" class="img-fluid rounded-circle mb-3" style="max-width: 150px;">
                        {% else %}
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 150px; height: 150px;">
                                <i class="fas fa-user fa-4x"></i>
                            </div>
                        {% endif %}
                        <h5>{{ user.full_name|default:user.username }}</h5>
                        <p class="text-muted">{{ user.get_user_type_display }}</p>
                    </div>
                    <div class="col-md-8">
                        <h6><i class="fas fa-info-circle me-1"></i>Personal Information</h6>
                        <div class="row">
                            <div class="col-sm-6">
                                <p><strong>Username:</strong> {{ user.username }}</p>
                                <p><strong>Email:</strong> {{ user.email }}</p>
                                <p><strong>Phone:</strong> {{ user.phone_number|default:"Not provided" }}</p>
                            </div>
                            <div class="col-sm-6">
                                <p><strong>User Type:</strong> {{ user.get_user_type_display }}</p>
                                <p><strong>Member Since:</strong> {{ user.date_joined|date:"M d, Y" }}</p>
                                {% if user.policy_number %}
                                    <p><strong>Policy Number:</strong> {{ user.policy_number }}</p>
                                {% endif %}
                            </div>
                        </div>

                        {% if user.address %}
                            <h6><i class="fas fa-map-marker-alt me-1"></i>Address</h6>
                            <p>{{ user.address|linebreaks }}</p>
                        {% endif %}

                        {% if profile.bio %}
                            <h6><i class="fas fa-file-alt me-1"></i>Bio</h6>
                            <p>{{ profile.bio|linebreaks }}</p>
                        {% endif %}

                        <h6><i class="fas fa-cog me-1"></i>Preferences</h6>
                        <p><strong>Preferred Communication:</strong> {{ profile.get_preferred_communication_display }}</p>

                        {% if profile.emergency_contact_name %}
                            <h6><i class="fas fa-phone me-1"></i>Emergency Contact</h6>
                            <p><strong>Name:</strong> {{ profile.emergency_contact_name }}</p>
                            {% if profile.emergency_contact_phone %}
                                <p><strong>Phone:</strong> {{ profile.emergency_contact_phone }}</p>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Account Summary
                </h6>
            </div>
            <div class="card-body">
                {% if user.user_type == 'customer' %}
                    <div class="text-center mb-3">
                        <div class="stat-number">{{ user.claims.count }}</div>
                        <div class="stat-label">Total Claims</div>
                    </div>
                    <div class="text-center mb-3">
                        <div class="stat-number">{{ user.claims.filter.status__in:'submitted,under_review'.count }}</div>
                        <div class="stat-label">Active Claims</div>
                    </div>
                {% elif user.user_type in 'adjuster,admin' %}
                    <div class="text-center mb-3">
                        <div class="stat-number">{{ user.assigned_claims.count }}</div>
                        <div class="stat-label">Assigned Claims</div>
                    </div>
                    <div class="text-center mb-3">
                        <div class="stat-number">{{ user.assigned_tasks.filter.status:'pending'.count }}</div>
                        <div class="stat-label">Pending Tasks</div>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>Account Security
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-2">
                    <strong>Last Login:</strong><br>
                    {{ user.last_login|date:"M d, Y g:i A"|default:"Never" }}
                </p>
                <p class="mb-0">
                    <strong>Account Status:</strong><br>
                    <span class="badge bg-{% if user.is_active %}success{% else %}danger{% endif %}">
                        {% if user.is_active %}Active{% else %}Inactive{% endif %}
                    </span>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
