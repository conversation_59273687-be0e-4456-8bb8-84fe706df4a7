{% extends 'base.html' %}
{% load static %}

{% block title %}Edit Profile - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'claims:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'users:profile' %}">Profile</a></li>
                <li class="breadcrumb-item active">Edit</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Edit Profile
                </h4>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <h6 class="mb-3"><i class="fas fa-user me-1"></i>Personal Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                {{ form.first_name }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                {{ form.last_name }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                                {{ form.email }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                                {{ form.phone_number }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">Address</label>
                        {{ form.address }}
                    </div>
                    
                    <hr>
                    
                    <h6 class="mb-3"><i class="fas fa-cog me-1"></i>Profile Settings</h6>
                    
                    <div class="mb-3">
                        <label for="{{ form.avatar.id_for_label }}" class="form-label">Profile Picture</label>
                        {{ form.avatar }}
                        {% if profile.avatar %}
                            <div class="mt-2">
                                <small class="text-muted">Current: {{ profile.avatar.name }}</small>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.bio.id_for_label }}" class="form-label">Bio</label>
                        {{ form.bio }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.preferred_communication.id_for_label }}" class="form-label">Preferred Communication Method</label>
                        {{ form.preferred_communication }}
                    </div>
                    
                    <hr>
                    
                    <h6 class="mb-3"><i class="fas fa-phone me-1"></i>Emergency Contact</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.emergency_contact_name.id_for_label }}" class="form-label">Emergency Contact Name</label>
                                {{ form.emergency_contact_name }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.emergency_contact_phone.id_for_label }}" class="form-label">Emergency Contact Phone</label>
                                {{ form.emergency_contact_phone }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'users:profile' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Account Information
                </h6>
            </div>
            <div class="card-body">
                <p><strong>Username:</strong> {{ user.username }}</p>
                <p><strong>User Type:</strong> {{ user.get_user_type_display }}</p>
                <p><strong>Member Since:</strong> {{ user.date_joined|date:"M d, Y" }}</p>
                {% if user.policy_number %}
                    <p class="mb-0"><strong>Policy Number:</strong> {{ user.policy_number }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>Privacy & Security
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-2">Your personal information is protected and will only be used for insurance-related purposes.</p>
                <p class="mb-0">
                    <small class="text-muted">
                        Last updated: {{ user.updated_at|date:"M d, Y" }}
                    </small>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
