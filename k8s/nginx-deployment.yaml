apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
    component: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: imt-insurance-claims
      component: nginx
  template:
    metadata:
      labels:
        app: imt-insurance-claims
        component: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        - containerPort: 443
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        - name: ssl-certs
          mountPath: /etc/nginx/ssl
          readOnly: true
        - name: static-files
          mountPath: /app/staticfiles
          readOnly: true
        - name: media-files
          mountPath: /app/media
          readOnly: true
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /health/
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
      - name: ssl-certs
        secret:
          secretName: tls-secret
      - name: static-files
        persistentVolumeClaim:
          claimName: static-pvc
      - name: media-files
        persistentVolumeClaim:
          claimName: media-pvc
      restartPolicy: Always
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
