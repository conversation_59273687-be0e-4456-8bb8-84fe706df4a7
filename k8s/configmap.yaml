apiVersion: v1
kind: ConfigMap
metadata:
  name: imt-claims-config
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
data:
  DJANGO_SETTINGS_MODULE: "claims_system.settings.production"
  DEBUG: "False"
  DB_ENGINE: "django.db.backends.postgresql"
  DB_NAME: "claims_db"
  DB_USER: "claims_user"
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  REDIS_URL: "redis://redis-service:6379/1"
  CELERY_BROKER_URL: "redis://redis-service:6379/0"
  CELERY_RESULT_BACKEND: "redis://redis-service:6379/0"
  ALLOWED_HOSTS: "demo.imtins.com,www.demo.imtins.com,localhost,127.0.0.1"
  EMAIL_HOST: "smtp.gmail.com"
  EMAIL_PORT: "587"
  EMAIL_USE_TLS: "True"
  DEFAULT_FROM_EMAIL: "<EMAIL>"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
    component: nginx
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        server_tokens off;

        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        upstream django_app {
            server django-service:8000;
            keepalive 32;
        }

        server {
            listen 80;
            server_name demo.imtins.com www.demo.imtins.com;

            location /health/ {
                proxy_pass http://django_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location / {
                return 301 https://$server_name$request_uri;
            }
        }

        server {
            listen 443 ssl http2;
            server_name demo.imtins.com www.demo.imtins.com;

            ssl_certificate /etc/nginx/ssl/tls.crt;
            ssl_certificate_key /etc/nginx/ssl/tls.key;
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
            ssl_prefer_server_ciphers off;
            ssl_session_cache shared:SSL:10m;
            ssl_session_timeout 10m;

            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

            location /static/ {
                alias /app/staticfiles/;
                expires 1y;
                add_header Cache-Control "public, immutable";
            }

            location /media/ {
                alias /app/media/;
                expires 1M;
                add_header Cache-Control "public";
            }

            location /health/ {
                proxy_pass http://django_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                access_log off;
            }

            location / {
                proxy_pass http://django_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
                
                proxy_buffering on;
                proxy_buffer_size 4k;
                proxy_buffers 8 4k;
                proxy_busy_buffers_size 8k;
            }
        }
    }
