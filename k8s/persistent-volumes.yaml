apiVersion: v1
kind: PersistentVolume
metadata:
  name: postgres-pv
  labels:
    app: imt-insurance-claims
    component: postgres
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: fast-ssd
  hostPath:
    path: /data/postgres

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
    component: postgres
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: media-pv
  labels:
    app: imt-insurance-claims
    component: media
spec:
  capacity:
    storage: 5Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: shared-storage
  hostPath:
    path: /data/media

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: media-pvc
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
    component: media
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: shared-storage

---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: static-pv
  labels:
    app: imt-insurance-claims
    component: static
spec:
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: shared-storage
  hostPath:
    path: /data/static

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: static-pvc
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
    component: static
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: shared-storage
