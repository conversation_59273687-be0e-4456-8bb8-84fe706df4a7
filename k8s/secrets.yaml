apiVersion: v1
kind: Secret
metadata:
  name: imt-claims-secrets
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  # To encode: echo -n "your-secret-value" | base64
  SECRET_KEY: aW10X3N1cGVyX3NlY3JldF9rZXlfZm9yX3Byb2R1Y3Rpb25fMjAyNF9jaGFuZ2VfdGhpcw==
  DB_PASSWORD: Y2xhaW1zX3Bhc3N3b3JkXzIwMjQ=
  REDIS_PASSWORD: cmVkaXNfcGFzc3dvcmRfMjAyNA==
  EMAIL_HOST_USER: ""
  EMAIL_HOST_PASSWORD: ""

---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
    component: postgres
type: Opaque
data:
  POSTGRES_DB: Y2xhaW1zX2Ri
  POSTGRES_USER: Y2xhaW1zX3VzZXI=
  POSTGRES_PASSWORD: Y2xhaW1zX3Bhc3N3b3JkXzIwMjQ=

---
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
    component: nginx
type: kubernetes.io/tls
data:
  # Replace with your actual SSL certificate and key (base64 encoded)
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCi4uLgotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0t
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCi4uLgotLS0tLUVORCBQUklWQVRFIEtFWS0tLS0t
