apiVersion: apps/v1
kind: Deployment
metadata:
  name: django-deployment
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
    component: django
spec:
  replicas: 3
  selector:
    matchLabels:
      app: imt-insurance-claims
      component: django
  template:
    metadata:
      labels:
        app: imt-insurance-claims
        component: django
    spec:
      initContainers:
      - name: migrate
        image: imt-claims:latest
        command: ['python', 'manage.py', 'migrate', '--noinput']
        envFrom:
        - configMapRef:
            name: imt-claims-config
        env:
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: SECRET_KEY
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: DB_PASSWORD
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: REDIS_PASSWORD
      containers:
      - name: django
        image: imt-claims:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: imt-claims-config
        env:
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: SECRET_KEY
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: DB_PASSWORD
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: REDIS_PASSWORD
        - name: EMAIL_HOST_USER
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: EMAIL_HOST_USER
        - name: EMAIL_HOST_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: EMAIL_HOST_PASSWORD
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
        - name: media-files
          mountPath: /app/media
        - name: logs
          mountPath: /app/logs
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health/
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: static-files
        persistentVolumeClaim:
          claimName: static-pvc
      - name: media-files
        persistentVolumeClaim:
          claimName: media-pvc
      - name: logs
        emptyDir: {}
      restartPolicy: Always
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
