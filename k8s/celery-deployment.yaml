apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-worker-deployment
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
    component: celery-worker
spec:
  replicas: 2
  selector:
    matchLabels:
      app: imt-insurance-claims
      component: celery-worker
  template:
    metadata:
      labels:
        app: imt-insurance-claims
        component: celery-worker
    spec:
      containers:
      - name: celery-worker
        image: imt-claims:latest
        command: ["celery", "-A", "claims_system", "worker", "-l", "info"]
        envFrom:
        - configMapRef:
            name: imt-claims-config
        env:
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: SECRET_KEY
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: DB_PASSWORD
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: REDIS_PASSWORD
        volumeMounts:
        - name: media-files
          mountPath: /app/media
        - name: logs
          mountPath: /app/logs
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          exec:
            command:
            - celery
            - -A
            - claims_system
            - inspect
            - ping
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
      volumes:
      - name: media-files
        persistentVolumeClaim:
          claimName: media-pvc
      - name: logs
        emptyDir: {}
      restartPolicy: Always

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-beat-deployment
  namespace: imt-claims
  labels:
    app: imt-insurance-claims
    component: celery-beat
spec:
  replicas: 1
  selector:
    matchLabels:
      app: imt-insurance-claims
      component: celery-beat
  template:
    metadata:
      labels:
        app: imt-insurance-claims
        component: celery-beat
    spec:
      containers:
      - name: celery-beat
        image: imt-claims:latest
        command: ["celery", "-A", "claims_system", "beat", "-l", "info"]
        envFrom:
        - configMapRef:
            name: imt-claims-config
        env:
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: SECRET_KEY
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: DB_PASSWORD
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-claims-secrets
              key: REDIS_PASSWORD
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "100m"
      volumes:
      - name: logs
        emptyDir: {}
      restartPolicy: Always
